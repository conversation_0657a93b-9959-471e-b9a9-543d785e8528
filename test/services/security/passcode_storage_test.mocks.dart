// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in voji/test/services/security/passcode_storage_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;

import 'package:firebase_auth/firebase_auth.dart' as _i6;
import 'package:firebase_core/firebase_core.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:voji/models/idea.dart' as _i3;
import 'package:voji/models/ideabook.dart' as _i2;
import 'package:voji/models/note.dart' as _i4;
import 'package:voji/services/firebase/firestore_service.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeIdeabook_0 extends _i1.SmartFake implements _i2.Ideabook {
  _FakeIdeabook_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdea_1 extends _i1.SmartFake implements _i3.Idea {
  _FakeIdea_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNote_2 extends _i1.SmartFake implements _i4.Note {
  _FakeNote_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseApp_3 extends _i1.SmartFake implements _i5.FirebaseApp {
  _FakeFirebaseApp_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionCodeInfo_4 extends _i1.SmartFake
    implements _i6.ActionCodeInfo {
  _FakeActionCodeInfo_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserCredential_5 extends _i1.SmartFake
    implements _i6.UserCredential {
  _FakeUserCredential_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmationResult_6 extends _i1.SmartFake
    implements _i6.ConfirmationResult {
  _FakeConfirmationResult_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirestoreService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirestoreService extends _i1.Mock implements _i7.FirestoreService {
  MockFirestoreService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.Stream<List<_i2.Ideabook>> listenToIdeabooks() =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeabooks, []),
            returnValue: _i8.Stream<List<_i2.Ideabook>>.empty(),
          )
          as _i8.Stream<List<_i2.Ideabook>>);

  @override
  _i8.Future<List<_i2.Ideabook>> getAllIdeabooks() =>
      (super.noSuchMethod(
            Invocation.method(#getAllIdeabooks, []),
            returnValue: _i8.Future<List<_i2.Ideabook>>.value(<_i2.Ideabook>[]),
          )
          as _i8.Future<List<_i2.Ideabook>>);

  @override
  _i8.Future<_i2.Ideabook?> getIdeabookById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeabookById, [id]),
            returnValue: _i8.Future<_i2.Ideabook?>.value(),
          )
          as _i8.Future<_i2.Ideabook?>);

  @override
  _i8.Future<_i2.Ideabook> createIdeabook(_i2.Ideabook? ideabook) =>
      (super.noSuchMethod(
            Invocation.method(#createIdeabook, [ideabook]),
            returnValue: _i8.Future<_i2.Ideabook>.value(
              _FakeIdeabook_0(
                this,
                Invocation.method(#createIdeabook, [ideabook]),
              ),
            ),
          )
          as _i8.Future<_i2.Ideabook>);

  @override
  _i8.Future<bool> updateIdeabook(_i2.Ideabook? ideabook) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdeabook, [ideabook]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<bool> deleteIdeabook(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteIdeabook, [id]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Stream<List<_i3.Idea>> listenToIdeas(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToIdeas, [ideabookId]),
            returnValue: _i8.Stream<List<_i3.Idea>>.empty(),
          )
          as _i8.Stream<List<_i3.Idea>>);

  @override
  _i8.Future<List<_i3.Idea>> getIdeasByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeasByIdeabookId, [ideabookId]),
            returnValue: _i8.Future<List<_i3.Idea>>.value(<_i3.Idea>[]),
          )
          as _i8.Future<List<_i3.Idea>>);

  @override
  _i8.Future<_i3.Idea?> getIdeaById(String? ideabookId, String? ideaId) =>
      (super.noSuchMethod(
            Invocation.method(#getIdeaById, [ideabookId, ideaId]),
            returnValue: _i8.Future<_i3.Idea?>.value(),
          )
          as _i8.Future<_i3.Idea?>);

  @override
  _i8.Future<_i3.Idea> createIdea(_i3.Idea? idea) =>
      (super.noSuchMethod(
            Invocation.method(#createIdea, [idea]),
            returnValue: _i8.Future<_i3.Idea>.value(
              _FakeIdea_1(this, Invocation.method(#createIdea, [idea])),
            ),
          )
          as _i8.Future<_i3.Idea>);

  @override
  _i8.Future<bool> updateIdea(_i3.Idea? idea) =>
      (super.noSuchMethod(
            Invocation.method(#updateIdea, [idea]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<bool> deleteIdea(String? ideabookId, String? ideaId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteIdea, [ideabookId, ideaId]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Stream<List<_i4.Note>> listenToNotes(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToNotes, [ideabookId]),
            returnValue: _i8.Stream<List<_i4.Note>>.empty(),
          )
          as _i8.Stream<List<_i4.Note>>);

  @override
  _i8.Future<List<_i4.Note>> getNotesByIdeabookId(String? ideabookId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotesByIdeabookId, [ideabookId]),
            returnValue: _i8.Future<List<_i4.Note>>.value(<_i4.Note>[]),
          )
          as _i8.Future<List<_i4.Note>>);

  @override
  _i8.Future<_i4.Note?> getNoteById(String? ideabookId, String? noteId) =>
      (super.noSuchMethod(
            Invocation.method(#getNoteById, [ideabookId, noteId]),
            returnValue: _i8.Future<_i4.Note?>.value(),
          )
          as _i8.Future<_i4.Note?>);

  @override
  _i8.Future<_i4.Note> createNote(_i4.Note? note) =>
      (super.noSuchMethod(
            Invocation.method(#createNote, [note]),
            returnValue: _i8.Future<_i4.Note>.value(
              _FakeNote_2(this, Invocation.method(#createNote, [note])),
            ),
          )
          as _i8.Future<_i4.Note>);

  @override
  _i8.Future<bool> updateNote(_i4.Note? note) =>
      (super.noSuchMethod(
            Invocation.method(#updateNote, [note]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<bool> deleteNote(String? ideabookId, String? noteId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNote, [ideabookId, noteId]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<bool> savePasscodeHash(String? passcodeHash) =>
      (super.noSuchMethod(
            Invocation.method(#savePasscodeHash, [passcodeHash]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<String?> getPasscodeHash() =>
      (super.noSuchMethod(
            Invocation.method(#getPasscodeHash, []),
            returnValue: _i8.Future<String?>.value(),
          )
          as _i8.Future<String?>);

  @override
  _i8.Future<bool> isPasscodeSet() =>
      (super.noSuchMethod(
            Invocation.method(#isPasscodeSet, []),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);

  @override
  _i8.Future<bool> validatePasscode(String? passcode) =>
      (super.noSuchMethod(
            Invocation.method(#validatePasscode, [passcode]),
            returnValue: _i8.Future<bool>.value(false),
          )
          as _i8.Future<bool>);
}

/// A class which mocks [FirebaseAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAuth extends _i1.Mock implements _i6.FirebaseAuth {
  MockFirebaseAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_3(this, Invocation.getter(#app)),
          )
          as _i5.FirebaseApp);

  @override
  set app(_i5.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set tenantId(String? tenantId) => super.noSuchMethod(
    Invocation.setter(#tenantId, tenantId),
    returnValueForMissingStub: null,
  );

  @override
  set customAuthDomain(String? customAuthDomain) => super.noSuchMethod(
    Invocation.setter(#customAuthDomain, customAuthDomain),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i8.Future<void> useAuthEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #useAuthEmulator,
              [host, port],
              {#automaticHostMapping: automaticHostMapping},
            ),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> applyActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#applyActionCode, [code]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<_i6.ActionCodeInfo> checkActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#checkActionCode, [code]),
            returnValue: _i8.Future<_i6.ActionCodeInfo>.value(
              _FakeActionCodeInfo_4(
                this,
                Invocation.method(#checkActionCode, [code]),
              ),
            ),
          )
          as _i8.Future<_i6.ActionCodeInfo>);

  @override
  _i8.Future<void> confirmPasswordReset({
    required String? code,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #code: code,
              #newPassword: newPassword,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<_i6.UserCredential> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#createUserWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<List<String>> fetchSignInMethodsForEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#fetchSignInMethodsForEmail, [email]),
            returnValue: _i8.Future<List<String>>.value(<String>[]),
          )
          as _i8.Future<List<String>>);

  @override
  _i8.Future<_i6.UserCredential> getRedirectResult() =>
      (super.noSuchMethod(
            Invocation.method(#getRedirectResult, []),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#getRedirectResult, []),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  bool isSignInWithEmailLink(String? emailLink) =>
      (super.noSuchMethod(
            Invocation.method(#isSignInWithEmailLink, [emailLink]),
            returnValue: false,
          )
          as bool);

  @override
  _i8.Stream<_i6.User?> authStateChanges() =>
      (super.noSuchMethod(
            Invocation.method(#authStateChanges, []),
            returnValue: _i8.Stream<_i6.User?>.empty(),
          )
          as _i8.Stream<_i6.User?>);

  @override
  _i8.Stream<_i6.User?> idTokenChanges() =>
      (super.noSuchMethod(
            Invocation.method(#idTokenChanges, []),
            returnValue: _i8.Stream<_i6.User?>.empty(),
          )
          as _i8.Stream<_i6.User?>);

  @override
  _i8.Stream<_i6.User?> userChanges() =>
      (super.noSuchMethod(
            Invocation.method(#userChanges, []),
            returnValue: _i8.Stream<_i6.User?>.empty(),
          )
          as _i8.Stream<_i6.User?>);

  @override
  _i8.Future<void> sendPasswordResetEmail({
    required String? email,
    _i6.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> sendSignInLinkToEmail({
    required String? email,
    required _i6.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSignInLinkToEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> setLanguageCode(String? languageCode) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguageCode, [languageCode]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> setSettings({
    bool? appVerificationDisabledForTesting = false,
    String? userAccessGroup,
    String? phoneNumber,
    String? smsCode,
    bool? forceRecaptchaFlow,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setSettings, [], {
              #appVerificationDisabledForTesting:
                  appVerificationDisabledForTesting,
              #userAccessGroup: userAccessGroup,
              #phoneNumber: phoneNumber,
              #smsCode: smsCode,
              #forceRecaptchaFlow: forceRecaptchaFlow,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> setPersistence(_i6.Persistence? persistence) =>
      (super.noSuchMethod(
            Invocation.method(#setPersistence, [persistence]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<_i6.UserCredential> signInAnonymously() =>
      (super.noSuchMethod(
            Invocation.method(#signInAnonymously, []),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInAnonymously, []),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.UserCredential> signInWithCredential(
    _i6.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCredential, [credential]),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithCredential, [credential]),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.UserCredential> signInWithCustomToken(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCustomToken, [token]),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithCustomToken, [token]),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.UserCredential> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.UserCredential> signInWithEmailLink({
    required String? email,
    required String? emailLink,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailLink, [], {
              #email: email,
              #emailLink: emailLink,
            }),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithEmailLink, [], {
                  #email: email,
                  #emailLink: emailLink,
                }),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.UserCredential> signInWithProvider(
    _i6.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithProvider, [provider]),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithProvider, [provider]),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<_i6.ConfirmationResult> signInWithPhoneNumber(
    String? phoneNumber, [
    _i6.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i8.Future<_i6.ConfirmationResult>.value(
              _FakeConfirmationResult_6(
                this,
                Invocation.method(#signInWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i8.Future<_i6.ConfirmationResult>);

  @override
  _i8.Future<_i6.UserCredential> signInWithPopup(_i6.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPopup, [provider]),
            returnValue: _i8.Future<_i6.UserCredential>.value(
              _FakeUserCredential_5(
                this,
                Invocation.method(#signInWithPopup, [provider]),
              ),
            ),
          )
          as _i8.Future<_i6.UserCredential>);

  @override
  _i8.Future<void> signInWithRedirect(_i6.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithRedirect, [provider]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<String> verifyPasswordResetCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPasswordResetCode, [code]),
            returnValue: _i8.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#verifyPasswordResetCode, [code]),
              ),
            ),
          )
          as _i8.Future<String>);

  @override
  _i8.Future<void> verifyPhoneNumber({
    String? phoneNumber,
    _i6.PhoneMultiFactorInfo? multiFactorInfo,
    required _i6.PhoneVerificationCompleted? verificationCompleted,
    required _i6.PhoneVerificationFailed? verificationFailed,
    required _i6.PhoneCodeSent? codeSent,
    required _i6.PhoneCodeAutoRetrievalTimeout? codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration? timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    _i6.MultiFactorSession? multiFactorSession,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #phoneNumber: phoneNumber,
              #multiFactorInfo: multiFactorInfo,
              #verificationCompleted: verificationCompleted,
              #verificationFailed: verificationFailed,
              #codeSent: codeSent,
              #codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
              #autoRetrievedSmsCodeForTesting: autoRetrievedSmsCodeForTesting,
              #timeout: timeout,
              #forceResendingToken: forceResendingToken,
              #multiFactorSession: multiFactorSession,
            }),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);

  @override
  _i8.Future<void> revokeTokenWithAuthorizationCode(
    String? authorizationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#revokeTokenWithAuthorizationCode, [
              authorizationCode,
            ]),
            returnValue: _i8.Future<void>.value(),
            returnValueForMissingStub: _i8.Future<void>.value(),
          )
          as _i8.Future<void>);
}
