import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voji/services/preferences/suggested_prompts_storage.dart';

void main() {
  group('SuggestedPromptsStorage', () {
    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test('saveSuggestedPrompts should save the prompts to shared preferences', () async {
      // Arrange
      const ideabookId = 'test-ideabook-id';
      final prompts = ['Prompt 1', 'Prompt 2', 'Prompt 3'];
      
      // Act
      final result = await SuggestedPromptsStorage.saveSuggestedPrompts(ideabookId, prompts);
      
      // Assert
      expect(result, true);
      
      // Verify the value was saved
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('voji_suggested_prompts');
      expect(jsonString, isNotNull);
      
      // Decode the JSON string
      final jsonMap = json.decode(jsonString!);
      expect(jsonMap, isA<Map>());
      expect(jsonMap[ideabookId], isA<List>());
      expect(jsonMap[ideabookId], equals(prompts));
    });

    test('loadSuggestedPrompts should return the saved prompts', () async {
      // Arrange
      const ideabookId = 'test-ideabook-id';
      final prompts = ['Prompt 1', 'Prompt 2', 'Prompt 3'];
      
      // Save the prompts
      await SuggestedPromptsStorage.saveSuggestedPrompts(ideabookId, prompts);
      
      // Act
      final result = await SuggestedPromptsStorage.loadSuggestedPrompts(ideabookId);
      
      // Assert
      expect(result, equals(prompts));
    });

    test('loadSuggestedPrompts should return an empty list when no prompts are saved', () async {
      // Arrange
      const ideabookId = 'test-ideabook-id';
      
      // Act
      final result = await SuggestedPromptsStorage.loadSuggestedPrompts(ideabookId);
      
      // Assert
      expect(result, isEmpty);
    });

    test('saveSuggestedPrompts should only keep the 5 most recent prompts', () async {
      // Arrange
      const ideabookId = 'test-ideabook-id';
      final prompts = ['Prompt 1', 'Prompt 2', 'Prompt 3', 'Prompt 4', 'Prompt 5', 'Prompt 6', 'Prompt 7'];
      
      // Act
      final result = await SuggestedPromptsStorage.saveSuggestedPrompts(ideabookId, prompts);
      
      // Assert
      expect(result, true);
      
      // Verify only 5 prompts were saved
      final savedPrompts = await SuggestedPromptsStorage.loadSuggestedPrompts(ideabookId);
      expect(savedPrompts.length, equals(5));
      expect(savedPrompts, equals(prompts.take(5).toList()));
    });

    test('saveSuggestedPrompts should handle multiple ideabooks', () async {
      // Arrange
      const ideabookId1 = 'test-ideabook-id-1';
      const ideabookId2 = 'test-ideabook-id-2';
      final prompts1 = ['Prompt 1-1', 'Prompt 1-2', 'Prompt 1-3'];
      final prompts2 = ['Prompt 2-1', 'Prompt 2-2', 'Prompt 2-3'];
      
      // Act
      await SuggestedPromptsStorage.saveSuggestedPrompts(ideabookId1, prompts1);
      await SuggestedPromptsStorage.saveSuggestedPrompts(ideabookId2, prompts2);
      
      // Assert
      final savedPrompts1 = await SuggestedPromptsStorage.loadSuggestedPrompts(ideabookId1);
      final savedPrompts2 = await SuggestedPromptsStorage.loadSuggestedPrompts(ideabookId2);
      
      expect(savedPrompts1, equals(prompts1));
      expect(savedPrompts2, equals(prompts2));
    });
  });
}
