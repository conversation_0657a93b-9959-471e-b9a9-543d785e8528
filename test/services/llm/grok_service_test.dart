import 'package:flutter_test/flutter_test.dart';
import 'package:voji/models/chat_message.dart';
import 'package:voji/services/firebase/cloud_function_service.dart';
import 'package:voji/services/llm/grok_service.dart';

// Custom mock implementation for testing
class TestCloudFunctionService implements CloudFunctionService {
  Map<String, dynamic>? lastRequestBody;
  String? lastProvider;

  @override
  Future<CloudFunctionResponse> callLlmApi({
    required String provider,
    required Map<String, dynamic> requestBody,
  }) async {
    lastProvider = provider;
    lastRequestBody = requestBody;

    return CloudFunctionResponse.success(
      data: {
        'choices': [
          {
            'message': {
              'content': 'Test response',
            },
          },
        ],
      },
    );
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('GrokService', () {
    late GrokService grokService;
    late TestCloudFunctionService testCloudFunctionService;

    setUp(() {
      testCloudFunctionService = TestCloudFunctionService();
      grokService = GrokService(
        model: 'test-model',
        reasoningEffort: 'low',
        cloudFunctionService: testCloudFunctionService,
      );
    });

    test('chat limits messages to 25 when more are provided', () async {
      // Arrange
      final messages = List.generate(30, (index) => ChatMessage(
        id: 'msg_$index',
        role: index % 2 == 0 ? MessageRole.user : MessageRole.assistant,
        content: 'Message $index',
        timestamp: DateTime.now().subtract(Duration(minutes: 30 - index)),
      ));

      // Act
      await grokService.chat(messages);

      // Assert
      expect(testCloudFunctionService.lastProvider, 'grok');
      expect(testCloudFunctionService.lastRequestBody != null, true);

      final sentMessages = testCloudFunctionService.lastRequestBody!['messages'] as List;

      // Assert that only 25 messages were sent
      expect(sentMessages.length, 25);

      // Verify the messages are the last 25 from the original list
      for (int i = 0; i < 25; i++) {
        final originalIndex = i + 5; // The last 25 messages (indexes 5-29)
        final sentMessage = sentMessages[i] as Map<String, dynamic>;
        expect(sentMessage['content'], 'Message $originalIndex');
      }
    });

    test('chat does not limit messages when fewer than 25 are provided', () async {
      // Arrange
      final messages = List.generate(10, (index) => ChatMessage(
        id: 'msg_$index',
        role: index % 2 == 0 ? MessageRole.user : MessageRole.assistant,
        content: 'Message $index',
        timestamp: DateTime.now().subtract(Duration(minutes: 10 - index)),
      ));

      // Act
      await grokService.chat(messages);

      // Assert
      expect(testCloudFunctionService.lastProvider, 'grok');
      expect(testCloudFunctionService.lastRequestBody != null, true);

      final sentMessages = testCloudFunctionService.lastRequestBody!['messages'] as List;

      // Assert that all 10 messages were sent
      expect(sentMessages.length, 10);
    });
  });
}
