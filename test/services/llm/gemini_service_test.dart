import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:voji/services/firebase/cloud_function_service.dart';
import 'package:voji/services/llm/llm_service.dart';

class MockCloudFunctionService extends Mock implements CloudFunctionService {}
void main() {
  group('GeminiService', () {
    late GeminiService geminiService;

    late MockCloudFunctionService mockCloudFunctionService;

    setUp(() {
      mockCloudFunctionService = MockCloudFunctionService();
      geminiService = GeminiService(
        model: 'test-model',
        cloudFunctionService: mockCloudFunctionService,
      );
    });

    test('transcribeAudio returns failure when file does not exist for new ideabook', () async {
      // Arrange
      const nonExistentFilePath = 'non_existent_file.aac';

      // Act
      final result = await geminiService.transcribeAudio(
        nonExistentFilePath,
        useCase: TranscriptionUseCase.newIdeabook,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, 'Audio file does not exist');
    });

    test('transcribeAudio returns failure when file does not exist for new idea', () async {
      // Arrange
      const nonExistentFilePath = 'non_existent_file.aac';

      // Act
      final result = await geminiService.transcribeAudio(
        nonExistentFilePath,
        useCase: TranscriptionUseCase.newIdea,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, 'Audio file does not exist');
    });

    // Skip HTTP client tests for now
    // In a real implementation, we would modify the service to accept a client for testing
  });
}
