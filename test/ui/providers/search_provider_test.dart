import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/search_provider.dart';

void main() {
  test('searchQueryProvider should initially be empty', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    expect(container.read(searchQueryProvider), '');
  });

  test('filteredIdeabooksProvider should filter ideabooks by name', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Create test ideabooks with different names
    final testIdeabooks = [
      Ideabook(
        id: '1',
        name: 'Work Ideas',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
      Ideabook(
        id: '2',
        name: 'Personal Ideas',
        color: IdeabookColor.blue,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
    ];

    // Test with empty search query
    expect(
      container.read(filteredIdeabooksProvider(testIdeabooks)).length,
      testIdeabooks.length,
    );

    // Set search query to 'Work'
    container.read(searchQueryProvider.notifier).state = 'Work';

    // Test with 'Work' search query
    final filteredIdeabooks = container.read(filteredIdeabooksProvider(testIdeabooks));
    expect(filteredIdeabooks.length, 1);
    expect(filteredIdeabooks.first.id, '1');
    expect(filteredIdeabooks.first.name, 'Work Ideas');

    // Change search query to 'Personal'
    container.read(searchQueryProvider.notifier).state = 'Personal';

    // Test with 'Personal' search query
    final personalFilteredIdeabooks = container.read(filteredIdeabooksProvider(testIdeabooks));
    expect(personalFilteredIdeabooks.length, 1);
    expect(personalFilteredIdeabooks.first.id, '2');
    expect(personalFilteredIdeabooks.first.name, 'Personal Ideas');

    // Clear search query
    container.read(searchQueryProvider.notifier).state = '';

    // Test with empty search query again
    expect(
      container.read(filteredIdeabooksProvider(testIdeabooks)).length,
      testIdeabooks.length,
    );
  });

  test('ideabookSearchFilterProvider should provide a filter', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Set search query to 'Work'
    container.read(searchQueryProvider.notifier).state = 'Work';

    // Get the filter
    final filter = container.read(ideabookSearchFilterProvider);
    
    // Check that the filter is active
    expect(filter.isActive, true);
    
    // Create test ideabooks
    final testIdeabooks = [
      Ideabook(
        id: '1',
        name: 'Work Ideas',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
      Ideabook(
        id: '2',
        name: 'Personal Ideas',
        color: IdeabookColor.blue,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
    ];
    
    // Apply the filter
    final filteredIdeabooks = filter.apply(testIdeabooks);
    
    // Verify the results
    expect(filteredIdeabooks.length, 1);
    expect(filteredIdeabooks.first.id, '1');
  });
}
