import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/color_filter_provider.dart';
import 'package:voji/ui/providers/combined_filter_provider.dart';
import 'package:voji/ui/providers/group_provider.dart';
import 'package:voji/ui/providers/search_provider.dart';
import 'package:voji/ui/providers/sort_provider.dart';

void main() {
  test('combinedIdeabookFilterProvider should combine filters', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Create test ideabooks
    final testIdeabooks = [
      Ideabook(
        id: '1',
        name: 'Red Work Ideas',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
      Ideabook(
        id: '2',
        name: 'Blue Personal Ideas',
        color: IdeabookColor.blue,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
      Ideabook(
        id: '3',
        name: 'Green Work Ideas',
        color: IdeabookColor.green,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
      Ideabook(
        id: '4',
        name: 'Red Personal Ideas',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        
      ),
    ];

    // Test with no filters
    container.read(searchQueryProvider.notifier).state = '';
    container.read(colorFilterProvider.notifier).state = null;

    // Get the combined filter
    var combinedFilter = container.read(combinedIdeabookFilterProvider);

    // Check that the filter is not active
    expect(combinedFilter.isActive, false);

    // Apply the filter
    var filteredIdeabooks = combinedFilter.apply(testIdeabooks);

    // Verify the results - should return all ideabooks
    expect(filteredIdeabooks.length, testIdeabooks.length);

    // Test with only search filter
    container.read(searchQueryProvider.notifier).state = 'Work';
    container.read(colorFilterProvider.notifier).state = null;

    // Get the combined filter
    combinedFilter = container.read(combinedIdeabookFilterProvider);

    // Check that the filter is active
    expect(combinedFilter.isActive, true);

    // Apply the filter
    filteredIdeabooks = combinedFilter.apply(testIdeabooks);

    // Verify the results
    expect(filteredIdeabooks.length, 2);
    expect(filteredIdeabooks.map((e) => e.id).toList()..sort(), ['1', '3']);

    // Test with only color filter
    container.read(searchQueryProvider.notifier).state = '';
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;

    // Get the combined filter
    combinedFilter = container.read(combinedIdeabookFilterProvider);

    // Check that the filter is active
    expect(combinedFilter.isActive, true);

    // Apply the filter
    filteredIdeabooks = combinedFilter.apply(testIdeabooks);

    // Verify the results
    expect(filteredIdeabooks.length, 2);
    expect(filteredIdeabooks.map((e) => e.id).toList()..sort(), ['1', '4']);

    // Test with both filters
    container.read(searchQueryProvider.notifier).state = 'Work';
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;

    // Get the combined filter
    combinedFilter = container.read(combinedIdeabookFilterProvider);

    // Check that the filter is active
    expect(combinedFilter.isActive, true);

    // Apply the filter
    filteredIdeabooks = combinedFilter.apply(testIdeabooks);

    // Verify the results
    expect(filteredIdeabooks.length, 1);
    expect(filteredIdeabooks.first.id, '1');
  });

  test('isFilterActiveProvider should check if any filter is active', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Test with no filters
    container.read(searchQueryProvider.notifier).state = '';
    container.read(colorFilterProvider.notifier).state = null;
    expect(container.read(isFilterActiveProvider), false);

    // Test with search filter
    container.read(searchQueryProvider.notifier).state = 'Work';
    container.read(colorFilterProvider.notifier).state = null;
    expect(container.read(isFilterActiveProvider), true);

    // Test with color filter
    container.read(searchQueryProvider.notifier).state = '';
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;
    expect(container.read(isFilterActiveProvider), true);

    // Test with both filters
    container.read(searchQueryProvider.notifier).state = 'Work';
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;
    expect(container.read(isFilterActiveProvider), true);
  });

  test('combinedFilteredIdeabooksProvider should group ideabooks by color when enabled', () {
    // Create test ideabooks with different colors and creation dates
    final testIdeabooks = [
      // Red ideabooks
      Ideabook(
        id: '1',
        name: 'Red Ideabook 1',
        color: IdeabookColor.red,
        createdAt: DateTime(2023, 1, 1), // Oldest
        updatedAt: DateTime(2023, 1, 1),
        
      ),
      Ideabook(
        id: '2',
        name: 'Red Ideabook 2',
        color: IdeabookColor.red,
        createdAt: DateTime(2023, 2, 1), // Newer
        updatedAt: DateTime(2023, 2, 1),
        
      ),
      // Green ideabooks
      Ideabook(
        id: '3',
        name: 'Green Ideabook 1',
        color: IdeabookColor.green,
        createdAt: DateTime(2023, 1, 15), // Older
        updatedAt: DateTime(2023, 1, 15),
        
      ),
      Ideabook(
        id: '4',
        name: 'Green Ideabook 2',
        color: IdeabookColor.green,
        createdAt: DateTime(2023, 3, 1), // Newest
        updatedAt: DateTime(2023, 3, 1),
        
      ),
      // Blue ideabook
      Ideabook(
        id: '5',
        name: 'Blue Ideabook',
        color: IdeabookColor.blue,
        createdAt: DateTime(2023, 2, 15),
        updatedAt: DateTime(2023, 2, 15),
        
      ),
      // Yellow ideabook
      Ideabook(
        id: '6',
        name: 'Yellow Ideabook',
        color: IdeabookColor.yellow,
        createdAt: DateTime(2023, 1, 10),
        updatedAt: DateTime(2023, 1, 10),
        
      ),
      // Orange ideabook
      Ideabook(
        id: '7',
        name: 'Orange Ideabook',
        color: IdeabookColor.orange,
        createdAt: DateTime(2023, 2, 10),
        updatedAt: DateTime(2023, 2, 10),
        
      ),
      // Purple ideabook
      Ideabook(
        id: '8',
        name: 'Purple Ideabook',
        color: IdeabookColor.purple,
        createdAt: DateTime(2023, 3, 10),
        updatedAt: DateTime(2023, 3, 10),
        
      ),
      // No color ideabook
      Ideabook(
        id: '9',
        name: 'No Color Ideabook',
        color: IdeabookColor.none,
        createdAt: DateTime(2023, 1, 5),
        updatedAt: DateTime(2023, 1, 5),
        
      ),
    ];

    // We'll use the test data directly in our overrides

    // Create a ProviderContainer with overrides
    final container = ProviderContainer(
      overrides: [
        // Override the combinedFilteredIdeabooksProvider to use our test data directly
        combinedFilteredIdeabooksProvider.overrideWith((ref) {
          // Get the group by color and sort order settings
          final groupByColor = ref.watch(groupByColorProvider);
          final sortOrder = ref.watch(ideabookSortOrderProvider);

          // Create a copy of the test data
          final filteredIdeabooks = List<Ideabook>.from(testIdeabooks);

          // If grouping by color is enabled, group ideabooks by color
          if (groupByColor) {
            // Define the color order as specified
            final colorOrder = [
              IdeabookColor.red,    // 1
              IdeabookColor.green,  // 2
              IdeabookColor.blue,   // 3
              IdeabookColor.yellow, // 4
              IdeabookColor.orange, // 5
              IdeabookColor.purple, // 6
              IdeabookColor.none,   // Put "none" color at the end
            ];

            // First sort by creation date within each color group
            filteredIdeabooks.sort((a, b) {
              if (sortOrder == SortOrder.ascending) {
                return a.createdAt.compareTo(b.createdAt); // Oldest first
              } else {
                return b.createdAt.compareTo(a.createdAt); // Newest first
              }
            });

            // Then sort by color according to the specified order
            filteredIdeabooks.sort((a, b) {
              final aIndex = colorOrder.indexOf(a.color);
              final bIndex = colorOrder.indexOf(b.color);
              return aIndex.compareTo(bIndex);
            });
          } else {
            // If not grouping by color, just sort by creation date
            filteredIdeabooks.sort((a, b) {
              if (sortOrder == SortOrder.ascending) {
                return a.createdAt.compareTo(b.createdAt); // Oldest first
              } else {
                return b.createdAt.compareTo(a.createdAt); // Newest first
              }
            });
          }

          return AsyncValue.data(filteredIdeabooks);
        }),
      ],
    );
    addTearDown(container.dispose);

    // Test with group by color disabled and ascending sort order
    container.read(groupByColorProvider.notifier).setGroupByColor(false);
    container.read(ideabookSortOrderProvider.notifier).setSortOrder(SortOrder.ascending);

    // Get the filtered ideabooks
    var filteredIdeabooksAsync = container.read(combinedFilteredIdeabooksProvider);

    // Verify the results - should be sorted by creation date (oldest first)
    expect(filteredIdeabooksAsync.hasValue, true);
    var ideabooks = filteredIdeabooksAsync.value!;

    // Check the order - should be sorted by creation date ascending
    expect(ideabooks[0].id, '1'); // Oldest (2023-01-01)
    expect(ideabooks[1].id, '9'); // (2023-01-05)
    expect(ideabooks[2].id, '6'); // (2023-01-10)

    // Test with group by color enabled and ascending sort order
    container.read(groupByColorProvider.notifier).setGroupByColor(true);

    // Get the filtered ideabooks
    filteredIdeabooksAsync = container.read(combinedFilteredIdeabooksProvider);

    // Verify the results - should be grouped by color in the specified order
    expect(filteredIdeabooksAsync.hasValue, true);
    ideabooks = filteredIdeabooksAsync.value!;

    // Check the order - should be grouped by color first, then sorted by creation date
    // Red group (first)
    expect(ideabooks[0].color, IdeabookColor.red);
    expect(ideabooks[1].color, IdeabookColor.red);
    expect(ideabooks[0].id, '1'); // Oldest red
    expect(ideabooks[1].id, '2'); // Newer red

    // Green group (second)
    expect(ideabooks[2].color, IdeabookColor.green);
    expect(ideabooks[3].color, IdeabookColor.green);
    expect(ideabooks[2].id, '3'); // Older green
    expect(ideabooks[3].id, '4'); // Newer green

    // Blue group (third)
    expect(ideabooks[4].color, IdeabookColor.blue);
    expect(ideabooks[4].id, '5');

    // Yellow group (fourth)
    expect(ideabooks[5].color, IdeabookColor.yellow);
    expect(ideabooks[5].id, '6');

    // Orange group (fifth)
    expect(ideabooks[6].color, IdeabookColor.orange);
    expect(ideabooks[6].id, '7');

    // Purple group (sixth)
    expect(ideabooks[7].color, IdeabookColor.purple);
    expect(ideabooks[7].id, '8');

    // No color group (last)
    expect(ideabooks[8].color, IdeabookColor.none);
    expect(ideabooks[8].id, '9');

    // Test with group by color enabled and descending sort order
    container.read(ideabookSortOrderProvider.notifier).setSortOrder(SortOrder.descending);

    // Get the filtered ideabooks
    filteredIdeabooksAsync = container.read(combinedFilteredIdeabooksProvider);

    // Verify the results - should be grouped by color, with newest first within each group
    expect(filteredIdeabooksAsync.hasValue, true);
    ideabooks = filteredIdeabooksAsync.value!;

    // Check the order - should be grouped by color first, then sorted by creation date descending
    // Red group (first)
    expect(ideabooks[0].color, IdeabookColor.red);
    expect(ideabooks[1].color, IdeabookColor.red);
    expect(ideabooks[0].id, '2'); // Newer red (now first because of descending order)
    expect(ideabooks[1].id, '1'); // Older red

    // Green group (second)
    expect(ideabooks[2].color, IdeabookColor.green);
    expect(ideabooks[3].color, IdeabookColor.green);
    expect(ideabooks[2].id, '4'); // Newer green
    expect(ideabooks[3].id, '3'); // Older green
  });
}
