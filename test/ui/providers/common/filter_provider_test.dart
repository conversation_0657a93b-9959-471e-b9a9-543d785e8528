import 'package:flutter_test/flutter_test.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/common/filter_provider.dart';

void main() {
  group('FilterProvider', () {
    test('should initially have no active filters', () {
      final filter = FilterProvider<Ideabook>();
      expect(filter.isActive, false);
    });

    test('should apply string contains filter', () {
      final filter = FilterProvider<Ideabook>();
      
      // Create test ideabooks
      final testIdeabooks = [
        Ideabook(
          id: '1',
          name: 'Work Ideas',
          color: IdeabookColor.red,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
        Ideabook(
          id: '2',
          name: 'Personal Ideas',
          color: IdeabookColor.blue,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
      ];
      
      // Add a string contains filter criterion
      filter.addCriterion(
        StringContainsFilterCriterion<Ideabook>(
          query: 'Work',
          stringExtractor: (ideabook) => ideabook.name,
        ),
      );
      
      // Apply the filter
      final filteredIdeabooks = filter.apply(testIdeabooks);
      
      // Verify the results
      expect(filteredIdeabooks.length, 1);
      expect(filteredIdeabooks.first.id, '1');
    });

    test('should apply equality filter', () {
      final filter = FilterProvider<Ideabook>();
      
      // Create test ideabooks
      final testIdeabooks = [
        Ideabook(
          id: '1',
          name: 'Red Ideabook',
          color: IdeabookColor.red,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
        Ideabook(
          id: '2',
          name: 'Blue Ideabook',
          color: IdeabookColor.blue,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
      ];
      
      // Add an equality filter criterion
      filter.addCriterion(
        EqualityFilterCriterion<Ideabook, IdeabookColor>(
          value: IdeabookColor.red,
          propertyExtractor: (ideabook) => ideabook.color,
        ),
      );
      
      // Apply the filter
      final filteredIdeabooks = filter.apply(testIdeabooks);
      
      // Verify the results
      expect(filteredIdeabooks.length, 1);
      expect(filteredIdeabooks.first.id, '1');
    });

    test('should apply multiple filters', () {
      final filter = FilterProvider<Ideabook>();
      
      // Create test ideabooks
      final testIdeabooks = [
        Ideabook(
          id: '1',
          name: 'Red Work Ideas',
          color: IdeabookColor.red,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
        Ideabook(
          id: '2',
          name: 'Blue Personal Ideas',
          color: IdeabookColor.blue,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
        Ideabook(
          id: '3',
          name: 'Red Personal Ideas',
          color: IdeabookColor.red,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
      ];
      
      // Add a string contains filter criterion
      filter.addCriterion(
        StringContainsFilterCriterion<Ideabook>(
          query: 'Work',
          stringExtractor: (ideabook) => ideabook.name,
        ),
      );
      
      // Add an equality filter criterion
      filter.addCriterion(
        EqualityFilterCriterion<Ideabook, IdeabookColor>(
          value: IdeabookColor.red,
          propertyExtractor: (ideabook) => ideabook.color,
        ),
      );
      
      // Apply the filter
      final filteredIdeabooks = filter.apply(testIdeabooks);
      
      // Verify the results
      expect(filteredIdeabooks.length, 1);
      expect(filteredIdeabooks.first.id, '1');
    });

    test('should ignore inactive filters', () {
      final filter = FilterProvider<Ideabook>();
      
      // Create test ideabooks
      final testIdeabooks = [
        Ideabook(
          id: '1',
          name: 'Red Work Ideas',
          color: IdeabookColor.red,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
        Ideabook(
          id: '2',
          name: 'Blue Personal Ideas',
          color: IdeabookColor.blue,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          
        ),
      ];
      
      // Add a string contains filter criterion with empty query (inactive)
      filter.addCriterion(
        StringContainsFilterCriterion<Ideabook>(
          query: '',
          stringExtractor: (ideabook) => ideabook.name,
        ),
      );
      
      // Add an equality filter criterion with null value (inactive)
      filter.addCriterion(
        EqualityFilterCriterion<Ideabook, IdeabookColor?>(
          value: null,
          propertyExtractor: (ideabook) => ideabook.color,
        ),
      );
      
      // Apply the filter
      final filteredIdeabooks = filter.apply(testIdeabooks);
      
      // Verify the results - should return all ideabooks since no filters are active
      expect(filteredIdeabooks.length, 2);
    });
  });
}
