import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/ideabook_color_provider.dart';
import 'package:voji/ui/providers/fake_data_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/color_picker.dart';

void main() {
  testWidgets('ColorPicker displays color options and updates ideabook color', (WidgetTester tester) async {
    // Create a test ideabook
    final testIdeabook = Ideabook(
      id: 'test-id',
      name: 'Test Ideabook',
      color: IdeabookColor.red,
      isLocked: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      
    );

    // Create a container for our providers
    final fakeNotifier = FakeIdeabooksNotifier()..state = [testIdeabook];
    final container = ProviderContainer(
      overrides: [
        fakeIdeabooksProvider.overrideWith((ref) => fakeNotifier),
        // Override the updateIdeabookColorProvider to use our fake notifier
        updateIdeabookColorProvider.overrideWith((ref) =>
          (String ideabookId, IdeabookColor newColor) {
            // Update the ideabook color using the fake notifier
            fakeNotifier.updateIdeabookColor(ideabookId, newColor);
            // Exit color picking mode
            ref.read(colorPickingIdeabookIdProvider.notifier).state = null;
          }
        ),
      ],
    );

    // Create mock theme data with the required extensions
    final theme = ThemeData.light().copyWith(
      extensions: [
        VojiColors.light,
        VojiTextStyles.light(VojiColors.light),
      ],
    );

    // Build the widget
    await tester.pumpWidget(
      UncontrolledProviderScope(
        container: container,
        child: MaterialApp(
          theme: theme,
          home: Scaffold(
            body: ColorPicker(
              ideabookId: testIdeabook.id,
              currentColor: testIdeabook.color,
            ),
          ),
        ),
      ),
    );

    // Verify that the color picker displays color options (5 colors + current color indicator)
    expect(find.byType(Container), findsNWidgets(6)); // 5 color options + current color indicator

    // We know the order of colors in the ColorPicker widget:
    // [red, yellow, orange, green, blue, purple]
    // Since red is filtered out (current color), the remaining colors are:
    // [yellow, orange, green, blue, purple]

    // Tap on the blue color option
    await tester.tap(find.byType(GestureDetector).at(4)); // Blue is at index 4 after filtering out red
    await tester.pump();

    // Verify that the ideabook color was updated
    final updatedIdeabooks = container.read(fakeIdeabooksProvider);
    expect(updatedIdeabooks.length, 1);
    expect(updatedIdeabooks[0].id, testIdeabook.id);
    expect(updatedIdeabooks[0].color, IdeabookColor.blue); // Blue is at index 4 in the ColorPicker

    // Verify that color picking mode was exited
    final colorPickingId = container.read(colorPickingIdeabookIdProvider);
    expect(colorPickingId, isNull);
  });
}
