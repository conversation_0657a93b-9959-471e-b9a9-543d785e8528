import 'package:flutter_test/flutter_test.dart';
import 'package:voji/models/chat_rate_limit.dart';
import 'package:voji/services/chat/chat_rate_limiter.dart';

void main() {
  group('ChatRateLimit', () {
    test('isLimitReached returns false for empty message list', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      expect(limit.isLimitReached([]), false);
    });
    
    test('isLimitReached returns false when limit not reached', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      final now = DateTime.now();
      final messageTimes = [
        now.subtract(const Duration(seconds: 10)),
        now.subtract(const Duration(seconds: 20)),
        now.subtract(const Duration(seconds: 30)),
      ];
      
      expect(limit.isLimitReached(messageTimes), false);
    });
    
    test('isLimitReached returns true when limit reached', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      final now = DateTime.now();
      final messageTimes = [
        now.subtract(const Duration(seconds: 10)),
        now.subtract(const Duration(seconds: 20)),
        now.subtract(const Duration(seconds: 30)),
        now.subtract(const Duration(seconds: 40)),
        now.subtract(const Duration(seconds: 50)),
      ];
      
      expect(limit.isLimitReached(messageTimes), true);
    });
    
    test('isLimitReached ignores messages outside time window', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      final now = DateTime.now();
      final messageTimes = [
        now.subtract(const Duration(seconds: 10)),
        now.subtract(const Duration(seconds: 20)),
        now.subtract(const Duration(seconds: 30)),
        now.subtract(const Duration(seconds: 70)), // Outside window
        now.subtract(const Duration(seconds: 80)), // Outside window
      ];
      
      expect(limit.isLimitReached(messageTimes), false);
    });
    
    test('getNextAvailableTime returns null when limit not reached', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      final now = DateTime.now();
      final messageTimes = [
        now.subtract(const Duration(seconds: 10)),
        now.subtract(const Duration(seconds: 20)),
        now.subtract(const Duration(seconds: 30)),
      ];
      
      expect(limit.getNextAvailableTime(messageTimes), null);
    });
    
    test('getNextAvailableTime returns correct time when limit reached', () {
      final limit = ChatRateLimit(
        maxMessages: 5,
        periodSeconds: 60,
        description: 'test',
      );
      
      final now = DateTime.now();
      final oldestRelevantMessage = now.subtract(const Duration(seconds: 50));
      final messageTimes = [
        now.subtract(const Duration(seconds: 10)),
        now.subtract(const Duration(seconds: 20)),
        now.subtract(const Duration(seconds: 30)),
        now.subtract(const Duration(seconds: 40)),
        oldestRelevantMessage,
      ];
      
      final expectedNextAvailable = oldestRelevantMessage.add(const Duration(seconds: 60));
      final actualNextAvailable = limit.getNextAvailableTime(messageTimes);
      
      expect(actualNextAvailable, isNotNull);
      
      // Allow a small tolerance for time differences during test execution
      final difference = actualNextAvailable!.difference(expectedNextAvailable).inMilliseconds.abs();
      expect(difference < 100, true);
    });
  });
  
  group('ChatRateLimiter', () {
    test('checkRateLimit returns correct result when no limit reached', () async {
      final limiter = ChatRateLimiter(rateLimits: [
        ChatRateLimit(
          maxMessages: 5,
          periodSeconds: 60,
          description: 'test',
        ),
      ]);
      
      // This test assumes an empty message log
      final result = await limiter.checkRateLimit();
      
      expect(result.isLimitReached, false);
      expect(result.nextAvailableTime, null);
      expect(result.message, null);
    });
  });
}
