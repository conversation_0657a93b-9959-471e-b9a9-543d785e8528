# Voji - Voice Journal App

A mobile app that helps quickly capture daily fragmented ideas.

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK (latest stable version)
- Android Studio or VS Code with Flutter extensions

### Installation

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Set up Firebase configuration (see Firebase Setup section)

### Firebase Setup

The app uses Firebase for authentication, Firestore for data storage, and Cloud Functions for server-side processing.

To set up Firebase:

1. Create a new Firebase project in the Firebase console
2. Add Android and iOS apps to your project
3. Download and add the configuration files to your project
4. Enable Authentication with Google Sign-In
5. Set up Firestore database with appropriate security rules

### Running the App

```bash
flutter run
```

## Project Structure

- `lib/models`: Data models
- `lib/repositories`: Repository classes for data access
- `lib/services`: Service classes
  - `lib/services/firebase`: Firebase and Firestore services
  - `lib/services/auth`: Authentication services
  - `lib/services/llm`: Language model services
  - `lib/services/storage`: Local file storage services
- `lib/ui`: UI components and screens
  - `lib/ui/providers`: State management providers
  - `lib/ui/screens`: App screens
  - `lib/ui/theme`: Theme configuration
  - `lib/ui/widgets`: Reusable widgets

## Features

- Create and manage ideabooks
- Add ideas to ideabooks
- Filter ideabooks by color
- Search ideabooks by name
- Light and dark theme support
