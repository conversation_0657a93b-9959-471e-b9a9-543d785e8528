#!/bin/bash

# Find all test files that might contain displayOrder
TEST_FILES=$(find test -name "*.dart" -type f -exec grep -l "displayOrder" {} \;)

# Loop through each file and remove the displayOrder parameter
for file in $TEST_FILES; do
  echo "Processing $file..."

  # Use sed to remove the displayOrder parameter from Ideabook constructor calls
  sed -i '' -E 's/displayOrder: [0-9]+,//g' "$file"

  echo "Done with $file"
done

echo "All test files updated!"
