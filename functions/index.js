/**
 * Firebase Cloud Functions for LLM API integration
 *
 * This module provides Firebase callable functions that serve as a proxy
 * for various LLM APIs (Gemini, Gemma, Grok). The functions handle authentication
 * and pass through requests and responses between the client app and the LLM APIs.
 */

const { onCall, HttpsError } = require("firebase-functions/v2/https");
// Import only the logging functions we need
const { error, log } = require("firebase-functions/logger");
const axios = require("axios");

// API keys (hardcoded as per requirements)
const GEMINI_API_KEY = "AIzaSyBtqWLLWrwdZyVP2pWml50fDkRInzhiC5Q";
const GROK_API_KEY = "************************************************************************************";
// <PERSON> uses the same API key as Gemini
const GEMMA_API_KEY = GEMINI_API_KEY;

/**
 * Retry a function with exponential backoff
 *
 * @param {Function} operation - The function to retry
 * @param {number} maxRetries - The maximum number of retries (default: 3)
 * @param {number} initialDelay - The initial delay in milliseconds (default: 500ms)
 * @param {number} maxDelay - The maximum delay in milliseconds (default: 5000ms)
 * @param {number} factor - The exponential factor for backoff (default: 2)
 * @param {string} operationName - Optional name for logging purposes
 * @returns {Promise<any>} - The result of the operation
 */
async function retryOperation(operation, maxRetries = 3, initialDelay = 500, maxDelay = 5000, factor = 2.0, operationName = 'operation') {
  let attempt = 0;
  let delay = initialDelay;

  while (true) {
    attempt++;
    try {
      log(`RetryUtil: Attempting ${operationName} (attempt ${attempt} of ${maxRetries + 1})`);
      const result = await operation();
      if (attempt > 1) {
        log(`RetryUtil: ${operationName} succeeded after ${attempt} attempts`);
      }
      return result;
    } catch (err) {
      if (attempt > maxRetries) {
        error(`RetryUtil: ${operationName} failed after ${maxRetries + 1} attempts`, err);
        throw err;
      }

      // Calculate next delay with exponential backoff
      delay = Math.min(delay * factor, maxDelay);

      log(`RetryUtil: ${operationName} failed (attempt ${attempt}), retrying in ${delay}ms: ${err.message}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Generic LLM API function that handles requests to different LLM providers
 *
 * Request format:
 * {
 *   provider: "gemini" | "gemma" | "grok",
 *   requestBody: {
 *     // Provider-specific request body
 *   }
 * }
 *
 * Response format:
 * {
 *   isSuccess: boolean,
 *   data: any, // Provider-specific response data
 *   errorMessage: string | null
 * }
 *
 * Note: This function has internal retry logic for LLM API calls (3 retries).
 * The client should only retry this cloud function 2 times at most.
 */
exports.callLanguageModelApi = onCall({
  enforceAppCheck: true, // Reject requests with missing or invalid App Check tokens.
  // Set max instances to handle concurrent requests
  maxInstances: 10,
  // Set timeout to 60 seconds to allow for retries
  timeoutSeconds: 60,
}, async (request) => {
  // Minimal logging for production
  try {
    const { provider, requestBody } = request.data;

    // Validate required parameters
    if (!provider) {
      throw new HttpsError(
        'invalid-argument',
        "Missing 'provider' parameter",
        {
          location: "callLanguageModelApi validation",
          timestamp: new Date().toISOString()
        }
      );
    }

    if (!requestBody) {
      throw new HttpsError(
        'invalid-argument',
        "Missing 'requestBody' parameter",
        {
          location: "callLanguageModelApi validation",
          timestamp: new Date().toISOString()
        }
      );
    }

    log(`Processing request for provider: ${provider}`);

    let result;
    switch (provider.toLowerCase()) {
      case "gemini":
        result = await handleGeminiRequest(requestBody);
        return result;
      case "gemma":
        result = await handleGemmaRequest(requestBody);
        return result;
      case "grok":
        result = await handleGrokRequest(requestBody);
        return result;
      default:
        throw new HttpsError(
          'invalid-argument',
          `Unsupported provider: ${provider}`,
          {
            location: "callLanguageModelApi provider routing",
            timestamp: new Date().toISOString(),
            providedValue: provider,
            supportedProviders: ["gemini", "gemma", "grok"]
          }
        );
    }
  } catch (err) {
    // Create error details for client
    const errorDetails = {
      message: err.message,
      stack: err.stack,
      location: "callLanguageModelApi main function",
      timestamp: new Date().toISOString()
    };

    // Log detailed error information
    error("Function execution failed", {
      errorMessage: err.message,
      stack: err.stack,
      location: "callLanguageModelApi main function"
    });

    // Throw an HttpsError that will be properly handled by the client
    throw new HttpsError('internal', `Error processing request: ${err.message}`, errorDetails);
  }
});

/**
 * Handle requests to the Gemini API
 *
 * @param {Object} requestBody - The request body to send to Gemini API
 * @return {Object} - The response from Gemini API
 */
async function handleGeminiRequest(requestBody) {
  try {
    // Extract the model from the request body or use a default
    const model = requestBody.model || "gemini-2.0-flash-lite";

    // Construct the Gemini API URL
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${GEMINI_API_KEY}`;

    // Define the operation to retry
    const makeApiRequest = async () => {
      try {
        // Make the API request
        const response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        return response;
      } catch (err) {
        // Add additional context to the error
        if (err.response) {
          err.statusCode = err.response.status;
          err.responseData = err.response.data;
        }
        throw err;
      }
    };

    // Execute the operation with retry logic
    const response = await retryOperation(
      makeApiRequest,
      3, // maxRetries
      500, // initialDelay
      5000, // maxDelay
      2.0, // factor
      "Gemini API request"
    );

    // Return successful result
    const result = {
      isSuccess: true,
      data: response.data,
      errorMessage: null,
    };

    return result;
  } catch (err) {
    // Create detailed error information
    const errorDetails = {
      message: err.message,
      stack: err.stack,
      location: "handleGeminiRequest function",
      timestamp: new Date().toISOString()
    };

    if (err.statusCode || err.response) {
      // Add response details to error information
      const status = err.statusCode || err.response?.status;
      const responseData = err.responseData || err.response?.data;

      errorDetails.responseStatus = status;
      errorDetails.responseData = responseData;

      // Log detailed error info for debugging
      error("Gemini API error after retries", {
        status: status,
        data: JSON.stringify(responseData),
        headers: err.response ? JSON.stringify(err.response.headers) : null,
        request: {
          url: err.request?.path,
          method: err.request?.method,
          data: requestBody ? JSON.stringify(requestBody).substring(0, 500) : null // Limit size for large requests
        }
      });

      throw new HttpsError(
        'internal',
        `Gemini API error: ${status}`,
        errorDetails
      );
    } else {
      // Log detailed error info for debugging
      error("Gemini API call failed after retries", {
        errorMessage: err.message,
        errorName: err.name,
        errorCode: err.code,
        stack: err.stack
      });

      throw new HttpsError(
        'internal',
        `Error calling Gemini API: ${err.message}`,
        errorDetails
      );
    }
  }
}

/**
 * Handle requests to the Gemma API
 *
 * @param {Object} requestBody - The request body to send to Gemma API
 * @return {Object} - The response from Gemma API
 */
async function handleGemmaRequest(requestBody) {
  try {
    // Extract the model from the request body or use a default
    const model = requestBody.model || "gemma-3-27b-it";

    // Construct the Gemma API URL (same endpoint as Gemini)
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${GEMMA_API_KEY}`;

    // Define the operation to retry
    const makeApiRequest = async () => {
      try {
        // Make the API request
        const response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        return response;
      } catch (err) {
        // Add additional context to the error
        if (err.response) {
          err.statusCode = err.response.status;
          err.responseData = err.response.data;
        }
        throw err;
      }
    };

    // Execute the operation with retry logic
    const response = await retryOperation(
      makeApiRequest,
      3, // maxRetries
      500, // initialDelay
      5000, // maxDelay
      2.0, // factor
      "Gemma API request"
    );

    // Return successful result
    const result = {
      isSuccess: true,
      data: response.data,
      errorMessage: null,
    };

    return result;
  } catch (err) {
    // Create detailed error information
    const errorDetails = {
      message: err.message,
      stack: err.stack,
      location: "handleGemmaRequest function",
      timestamp: new Date().toISOString()
    };

    if (err.statusCode || err.response) {
      // Add response details to error information
      const status = err.statusCode || err.response?.status;
      const responseData = err.responseData || err.response?.data;

      errorDetails.responseStatus = status;
      errorDetails.responseData = responseData;

      // Log detailed error info for debugging
      error("Gemma API error after retries", {
        status: status,
        data: JSON.stringify(responseData),
        headers: err.response ? JSON.stringify(err.response.headers) : null,
        request: {
          url: err.request?.path,
          method: err.request?.method,
          data: requestBody ? JSON.stringify(requestBody).substring(0, 500) : null // Limit size for large requests
        }
      });

      throw new HttpsError(
        'internal',
        `Gemma API error: ${status}`,
        errorDetails
      );
    } else {
      // Log detailed error info for debugging
      error("Gemma API call failed after retries", {
        errorMessage: err.message,
        errorName: err.name,
        errorCode: err.code,
        stack: err.stack
      });

      throw new HttpsError(
        'internal',
        `Error calling Gemma API: ${err.message}`,
        errorDetails
      );
    }
  }
}

/**
 * Handle requests to the Grok API
 *
 * @param {Object} requestBody - The request body to send to Grok API
 * @return {Object} - The response from Grok API
 */
async function handleGrokRequest(requestBody) {
  try {
    // Construct the Grok API URL
    const apiUrl = "https://api.x.ai/v1/chat/completions";

    // Prepare headers
    const headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": `Bearer ${GROK_API_KEY}`,
      "Accept": "application/json; charset=utf-8",
    };

    // Define the operation to retry
    const makeApiRequest = async () => {
      try {
        // Make the API request
        const response = await axios.post(apiUrl, requestBody, { headers });
        return response;
      } catch (err) {
        // Add additional context to the error
        if (err.response) {
          err.statusCode = err.response.status;
          err.responseData = err.response.data;
        }
        throw err;
      }
    };

    // Execute the operation with retry logic
    const response = await retryOperation(
      makeApiRequest,
      3, // maxRetries
      500, // initialDelay
      5000, // maxDelay
      2.0, // factor
      "Grok API request"
    );

    // Return successful result
    const result = {
      isSuccess: true,
      data: response.data,
      errorMessage: null,
    };

    return result;
  } catch (err) {
    // Create detailed error information
    const errorDetails = {
      message: err.message,
      stack: err.stack,
      location: "handleGrokRequest function",
      timestamp: new Date().toISOString()
    };

    if (err.statusCode || err.response) {
      // Add response details to error information
      const status = err.statusCode || err.response?.status;
      const responseData = err.responseData || err.response?.data;

      errorDetails.responseStatus = status;
      errorDetails.responseData = responseData;

      // Log detailed error info for debugging
      error("Grok API error after retries", {
        status: status,
        data: JSON.stringify(responseData),
        headers: err.response ? JSON.stringify(err.response.headers) : null,
        request: {
          url: err.request?.path,
          method: err.request?.method,
          data: requestBody ? JSON.stringify(requestBody).substring(0, 500) : null // Limit size for large requests
        }
      });

      throw new HttpsError(
        'internal',
        `Grok API error: ${status}`,
        errorDetails
      );
    } else {
      // Log detailed error info for debugging
      error("Grok API call failed after retries", {
        errorMessage: err.message,
        errorName: err.name,
        errorCode: err.code,
        stack: err.stack
      });

      throw new HttpsError(
        'internal',
        `Error calling Grok API: ${err.message}`,
        errorDetails
      );
    }
  }
}
