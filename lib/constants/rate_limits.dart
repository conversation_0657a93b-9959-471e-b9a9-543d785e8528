import 'package:voji/models/chat_rate_limit.dart';
import 'package:voji/utils/logger.dart';

/// Constants for rate limits
class RateLimits {
  /// Default rate limits for chat messages
  static final List<ChatRateLimit> defaultChatRateLimits = [
    // 30 messages per minute
    const ChatRateLimit(
      maxMessages: 20,
      periodSeconds: 60,
      description: 'minute',
    ),
    // 1000 messages per day
    const ChatRateLimit(
      maxMessages: 1000,
      periodSeconds: 86400, // 24 hours in seconds
      description: 'day',
    ),
    // 10000 messages per month (30 days)
    const ChatRateLimit(
      maxMessages: 10000,
      periodSeconds: 2592000, // 30 days in seconds
      description: 'month',
    ),
  ];

  /// Log the current rate limits for debugging
  static void logRateLimits() {
    Logger.debug('===== RATE LIMITS CONFIGURATION =====');
    for (final limit in defaultChatRateLimits) {
      Logger.debug('Rate limit: max ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds} seconds)');
    }
    Logger.debug('====================================');
  }
}
