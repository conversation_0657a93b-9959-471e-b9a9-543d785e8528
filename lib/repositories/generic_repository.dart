import 'package:voji/models/base_model.dart';
import 'package:voji/repositories/base_repository.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';

/// A generic repository implementation that can be extended by specific repositories
///
/// Type parameters:
/// - T: The model type (must extend BaseModel)
abstract class GenericRepository<T extends BaseModel> implements BaseRepository<T> {
  /// Creates a new GenericRepository
  GenericRepository();

  /// Log repository operations with consistent format
  @override
  void logOperation(String operation, {String? id, String? details, Object? error}) {
    final className = runtimeType.toString();

    if (error != null) {
      Logger.error('$className: $operation failed${id != null ? ' for ID $id' : ''}${details != null ? ' - $details' : ''}', error);
    } else {
      Logger.debug('$className: $operation${id != null ? ' for ID $id' : ''}${details != null ? ' - $details' : ''}');
    }
  }

  @override
  Future<List<T>> getAll() async {
    logOperation('getAll');
    throw UnimplementedError('This method should be overridden by subclasses');
  }

  @override
  Future<T?> getById(String id) async {
    logOperation('getById', id: id);
    throw UnimplementedError('This method should be overridden by subclasses');
  }

  @override
  Future<T> create(Map<String, dynamic> data) async {
    logOperation('create');
    throw UnimplementedError('This method should be overridden by subclasses');
  }

  @override
  Future<bool> update(T entity) async {
    logOperation('update', id: entity.id);
    throw UnimplementedError('This method should be overridden by subclasses');
  }

  @override
  Future<bool> delete(String id) async {
    logOperation('delete', id: id);
    throw UnimplementedError('This method should be overridden by subclasses');
  }
}
