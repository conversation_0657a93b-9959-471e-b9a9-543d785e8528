import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/repositories/chat_file_repository.dart';
import 'package:voji/services/storage/storage_providers.dart';

/// Provider for the ChatFileRepository
final chatFileRepositoryProvider = Provider<ChatFileRepository>((ref) {
  final chatFileStorage = ref.watch(chatFileStorageProvider);
  return ChatFileRepository(chatFileStorage);
});
