import 'dart:convert';

import 'package:voji/models/chat.dart';
import 'package:voji/models/chat_message.dart';
import 'package:voji/services/storage/local_file_storage.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';

/// Repository for managing Chat data using local file storage
class ChatFileRepository {
  /// Storage service for chat files
  final ChatFileStorage _storage;

  /// Constructor
  ChatFileRepository(this._storage);

  /// Log repository operations for debugging
  void _logOperation(String operation, {String? id, String? details, Object? error}) {
    final message = 'ChatFileRepository.$operation${id != null ? '($id)' : ''}${details != null ? ': $details' : ''}';
    if (error != null) {
      Logger.error(message, error);
    } else {
      Logger.debug(message);
    }
  }

  /// Get a chat by ideabook ID
  Future<Chat?> getChatByIdeabookId(String ideabookId) async {
    _logOperation('getChatByIdeabookId', id: ideabookId);
    try {
      final chatData = await _storage.readChat(ideabookId);
      if (chatData == null) {
        _logOperation('getChatByIdeabookId', id: ideabookId, details: 'Not found');
        return null;
      }

      final chat = Chat.fromMessagesJson(
        id: chatData['id'] as String,
        ideabookId: chatData['ideabookId'] as String,
        messagesJson: chatData['messagesJson'] as String,
        createdAt: DateTime.parse(chatData['createdAt'] as String),
        updatedAt: DateTime.parse(chatData['updatedAt'] as String),
      );

      _logOperation('getChatByIdeabookId', id: ideabookId, details: 'Found chat with ${chat.messages.length} messages');
      return chat;
    } catch (e) {
      _logOperation('getChatByIdeabookId', id: ideabookId, error: e);
      rethrow;
    }
  }

  /// Create a new chat
  Future<Chat> createChat({
    required String ideabookId,
    List<ChatMessage> messages = const [],
  }) async {
    _logOperation('createChat', details: 'For ideabook: $ideabookId');

    try {
      final id = IdUtils.generateId();
      final now = DateTime.now();

      final chat = Chat(
        id: id,
        ideabookId: ideabookId,
        messages: messages,
        createdAt: now,
        updatedAt: now,
      );

      final chatData = {
        'id': chat.id,
        'ideabookId': chat.ideabookId,
        'messagesJson': chat.messagesJson,
        'createdAt': chat.createdAt.toIso8601String(),
        'updatedAt': chat.updatedAt.toIso8601String(),
      };

      final success = await _storage.writeChat(ideabookId, chatData);
      if (!success) {
        throw Exception('Failed to write chat data to file');
      }

      _logOperation('createChat', id: id, details: 'Chat created successfully');
      return chat;
    } catch (e) {
      _logOperation('createChat', details: 'For ideabook: $ideabookId', error: e);
      rethrow;
    }
  }

  /// Update an existing chat
  Future<bool> updateChat(Chat chat) async {
    _logOperation('updateChat', id: chat.id);

    try {
      final updatedChat = chat.copyWith(
        updatedAt: DateTime.now(),
      );

      final chatData = {
        'id': updatedChat.id,
        'ideabookId': updatedChat.ideabookId,
        'messagesJson': updatedChat.messagesJson,
        'createdAt': updatedChat.createdAt.toIso8601String(),
        'updatedAt': updatedChat.updatedAt.toIso8601String(),
      };

      final success = await _storage.writeChat(chat.ideabookId, chatData);
      _logOperation('updateChat', id: chat.id, details: 'Chat updated successfully: $success');
      return success;
    } catch (e) {
      _logOperation('updateChat', id: chat.id, error: e);
      return false;
    }
  }

  /// Add a message to a chat
  Future<bool> addMessageToChat({
    required Chat chat,
    required MessageRole role,
    required String content,
    Map<String, dynamic>? metadata,
  }) async {
    _logOperation('addMessageToChat', id: chat.id, details: 'Adding ${role.name} message');

    if (metadata != null) {
      _logOperation('addMessageToChat', id: chat.id, details: 'Message has metadata: ${metadata.keys.join(', ')}');
      if (metadata.containsKey('user_prompt')) {
        _logOperation('addMessageToChat', id: chat.id, details: 'Message has user_prompt: "${metadata['user_prompt']}"');
      }
    } else {
      _logOperation('addMessageToChat', id: chat.id, details: 'Message has no metadata');
    }

    try {
      final messageId = IdUtils.generateId();
      final now = DateTime.now();

      // Ensure proper encoding of the content
      final sanitizedContent = _ensureProperEncoding(content);

      final message = ChatMessage(
        id: messageId,
        role: role,
        content: sanitizedContent,
        timestamp: now,
        metadata: metadata,
      );

      final updatedMessages = [...chat.messages, message];
      final updatedChat = chat.copyWith(
        messages: updatedMessages,
        updatedAt: now,
      );

      final success = await updateChat(updatedChat);
      _logOperation('addMessageToChat', id: chat.id, details: 'Message added successfully: $success');
      return success;
    } catch (e) {
      _logOperation('addMessageToChat', id: chat.id, error: e);
      rethrow;
    }
  }

  /// Delete a chat
  Future<bool> deleteChat(String ideabookId) async {
    _logOperation('deleteChat', id: ideabookId);

    try {
      final success = await _storage.deleteChat(ideabookId);
      _logOperation('deleteChat', id: ideabookId, details: 'Chat deleted successfully: $success');
      return success;
    } catch (e) {
      _logOperation('deleteChat', id: ideabookId, error: e);
      return false;
    }
  }

  /// Update a specific message in a chat
  Future<bool> updateMessage({
    required Chat chat,
    required String messageId,
    required ChatMessage updatedMessage,
  }) async {
    _logOperation('updateMessage', id: messageId, details: 'Updating message in chat ${chat.id}');

    try {
      // Find the message in the chat
      final messageIndex = chat.messages.indexWhere((m) => m.id == messageId);
      if (messageIndex == -1) {
        _logOperation('updateMessage', id: messageId, details: 'Message not found in chat ${chat.id}');
        return false;
      }

      // Create a new list with the updated message
      final updatedMessages = List<ChatMessage>.from(chat.messages);
      updatedMessages[messageIndex] = updatedMessage;

      // Update the chat with the new messages list
      final updatedChat = chat.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      // Save the updated chat
      final success = await updateChat(updatedChat);
      _logOperation('updateMessage', id: messageId, details: 'Message updated successfully: $success');
      return success;
    } catch (e) {
      _logOperation('updateMessage', id: messageId, error: e);
      return false;
    }
  }

  /// Ensure proper encoding of the content
  /// This helps with non-ASCII characters like Chinese
  String _ensureProperEncoding(String content) {
    try {
      // First, check if the content is already valid UTF-8
      if (_isValidUtf8(content)) {
        return content;
      }

      // If not, try to decode and re-encode the content
      final decoded = utf8.decode(utf8.encode(content));
      return decoded;
    } catch (e) {
      _logOperation('_ensureProperEncoding', error: e);
      return content; // Return original content if encoding fails
    }
  }

  /// Check if a string is valid UTF-8
  bool _isValidUtf8(String text) {
    try {
      utf8.decode(utf8.encode(text));
      return true;
    } catch (e) {
      return false;
    }
  }
}
