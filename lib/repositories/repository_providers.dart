import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/repositories/repositories.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/storage/storage_providers.dart';

/// Provider for the IdeabookRepository
final ideabookRepositoryProvider = Provider<IdeabookRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final chatFileStorage = ref.watch(chatFileStorageProvider);

  return IdeabookRepository(
    firestoreService: firestoreService,
    chatFileStorage: chatFileStorage,
  );
});

/// Provider for the IdeaRepository
final ideaRepositoryProvider = Provider<IdeaRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return IdeaRepository(firestoreService: firestoreService);
});

/// Provider for the NoteRepository
final noteRepositoryProvider = Provider<NoteRepository>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);

  return NoteRepository(
    firestoreService: firestoreService,
  );
});
