import 'package:voji/constants.dart';
import 'package:voji/models/models.dart' as models;
import 'package:voji/repositories/generic_repository.dart';
import 'package:voji/services/firebase/firestore_service.dart';
import 'package:voji/utils/id_utils.dart';

/// Repository for managing Idea data
class IdeaRepository extends GenericRepository<models.Idea> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Creates a new IdeaRepository
  IdeaRepository({
    required FirestoreService firestoreService,
  }) : _firestoreService = firestoreService,
      super();

  /// Get all ideas for an ideabook
  Future<List<models.Idea>> getIdeasByIdeabookId(String ideabookId) async {
    logOperation('getIdeasByIdeabookId', id: ideabookId, details: 'Using Firestore with listener');
    try {
      // Use the listener to get all ideas
      return await _firestoreService.listenToIdeas(ideabookId).first;
    } catch (e) {
      logOperation('getIdeasByIdeabookId', id: ideabookId, error: e);
      rethrow;
    }
  }

  /// Get a stream of all ideas for an ideabook
  Stream<List<models.Idea>> getIdeasByIdeabookIdStream(String ideabookId) {
    logOperation('getIdeasByIdeabookIdStream', id: ideabookId, details: 'Using Firestore listener');
    return _firestoreService.listenToIdeas(ideabookId);
  }

  @override
  Future<List<models.Idea>> getAll() async {
    logOperation('getAll');
    throw UnimplementedError('Use getIdeasByIdeabookId instead');
  }

  @override
  Future<models.Idea?> getById(String id) async {
    logOperation('getById', id: id);
    throw UnimplementedError('Use getIdeaById with ideabookId instead');
  }

  /// Get an idea by ID
  Future<models.Idea?> getIdeaById(String ideabookId, String id) async {
    logOperation('getIdeaById', id: id, details: 'Using Firestore with listener');
    try {
      // Use the listener to get the idea
      return await _firestoreService.listenToIdeaById(ideabookId, id).first;
    } catch (e) {
      logOperation('getIdeaById', id: id, error: e);
      rethrow;
    }
  }

  /// Get a stream of an idea by ID
  Stream<models.Idea?> getIdeaByIdStream(String ideabookId, String id) {
    logOperation('getIdeaByIdStream', id: id, details: 'Using Firestore listener');
    return _firestoreService.listenToIdeaById(ideabookId, id);
  }

  @override
  Future<models.Idea> create(Map<String, dynamic> data) async {
    logOperation('create');
    try {
      final ideabookId = data['ideabookId'] as String;
      final content = data['content'] as String;

      // Create the idea model with current time as placeholder
      // The actual creation timestamp will be set by Firestore's serverTimestamp()
      final now = DateTime.now();
      final idea = models.Idea(
        id: IdUtils.generateId(),
        content: content,
        createdAt: now, // This will be replaced by server timestamp
        updatedAt: now,
      );

      return await _firestoreService.createIdea(ideabookId, idea);
    } catch (e) {
      logOperation('create', error: e);
      rethrow;
    }
  }

  /// Get the count of ideas in an ideabook
  Future<int> getIdeaCount(String ideabookId) async {
    logOperation('getIdeaCount', id: ideabookId);
    try {
      final ideas = await getIdeasByIdeabookId(ideabookId);
      return ideas.length;
    } catch (e) {
      logOperation('getIdeaCount', id: ideabookId, error: e);
      rethrow;
    }
  }

  /// Check if an ideabook has reached the maximum number of ideas
  Future<bool> isIdeabookFull(String ideabookId) async {
    logOperation('isIdeabookFull', id: ideabookId);
    try {
      final count = await getIdeaCount(ideabookId);
      return count >= kMaxIdeasPerIdeabook;
    } catch (e) {
      logOperation('isIdeabookFull', id: ideabookId, error: e);
      // In case of error, assume not full to avoid blocking the user
      return false;
    }
  }

  /// Create a new idea
  Future<models.Idea> createIdea({
    required String ideabookId,
    required String content,
  }) async {
    logOperation('createIdea', details: 'For ideabook: $ideabookId');

    // Check if the ideabook is full
    final isFull = await isIdeabookFull(ideabookId);
    if (isFull) {
      throw Exception('Ideabook is full. Maximum of $kMaxIdeasPerIdeabook ideas reached.');
    }

    // For transcribed audio, we allow saving even if it's over the limit
    // The user will be prevented from editing and saving it later if it's over the limit
    // This is handled in the UI layer in the _saveChanges method of SwipeableIdeaItem

    return create({
      'ideabookId': ideabookId,
      'content': content,
    });
  }

  @override
  Future<bool> update(models.Idea entity) async {
    logOperation('update', id: entity.id);
    try {
      // Since we don't have ideabookId in the entity anymore, we need to get it from the context
      // This method should be called with the ideabookId parameter
      throw UnimplementedError('Use updateIdea with ideabookId instead');
    } catch (e) {
      logOperation('update', id: entity.id, error: e);
      rethrow;
    }
  }

  /// Update an existing idea
  Future<bool> updateIdea(String ideabookId, models.Idea idea) async {
    logOperation('updateIdea', id: idea.id);
    try {
      return await _firestoreService.updateIdea(ideabookId, idea);
    } catch (e) {
      logOperation('updateIdea', id: idea.id, error: e);
      rethrow;
    }
  }

  @override
  Future<bool> delete(String id) async {
    logOperation('delete', id: id);
    throw UnimplementedError('Use deleteIdea with ideabookId instead');
  }

  /// Delete an idea
  Future<bool> deleteIdea(String ideabookId, String id) async {
    logOperation('deleteIdea', id: id);
    try {
      return await _firestoreService.deleteIdea(ideabookId, id);
    } catch (e) {
      logOperation('deleteIdea', id: id, error: e);
      rethrow;
    }
  }
}
