import 'dart:convert';

import 'package:voji/models/base_model.dart';
import 'package:voji/models/chat_message.dart';
import 'package:voji/utils/logger.dart';

/// Represents a Chat, which is a conversation with <PERSON><PERSON> about an ideabook
class Chat extends BaseModel {
  /// ID of the ideabook this chat belongs to
  final String ideabookId;

  /// List of messages in the chat
  final List<ChatMessage> messages;

  /// Creates a new Chat instance
  const Chat({
    required super.id,
    required this.ideabookId,
    required this.messages,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Creates a copy of this Chat with the given fields replaced with new values
  @override
  Chat copyWith({
    String? id,
    String? ideabookId,
    List<ChatMessage>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Chat(
      id: id ?? this.id,
      ideabookId: ideabookId ?? this.ideabookId,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converts the messages to a JSON string
  String get messagesJson {
    return jsonEncode(messages.map((message) => message.toJson()).toList());
  }

  /// Creates a Chat from a JSON string of messages
  factory Chat.fromMessagesJson({
    required String id,
    required String ideabookId,
    required String messagesJson,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) {
    try {
      // Ensure proper UTF-8 encoding of the JSON string
      final sanitizedJson = _ensureProperEncoding(messagesJson);

      final List<dynamic> jsonList = jsonDecode(sanitizedJson);
      final List<ChatMessage> messages = jsonList
          .map((json) => ChatMessage.fromJson(json as Map<String, dynamic>))
          .toList();

      return Chat(
        id: id,
        ideabookId: ideabookId,
        messages: messages,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e) {
      // If there's an error, try with the original JSON
      Logger.error('Error decoding chat messages JSON', e);

      final List<dynamic> jsonList = jsonDecode(messagesJson);
      final List<ChatMessage> messages = jsonList
          .map((json) => ChatMessage.fromJson(json as Map<String, dynamic>))
          .toList();

      return Chat(
        id: id,
        ideabookId: ideabookId,
        messages: messages,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    }
  }

  /// Ensure proper encoding of a string
  static String _ensureProperEncoding(String content) {
    try {
      // First, check if the content is already valid UTF-8
      if (_isValidUtf8(content)) {
        return content;
      }

      // If not, try to decode and re-encode the content
      final decoded = utf8.decode(utf8.encode(content));
      return decoded;
    } catch (e) {
      Logger.error('Error ensuring proper encoding', e);
      return content; // Return original content if encoding fails
    }
  }

  /// Check if a string is valid UTF-8
  static bool _isValidUtf8(String text) {
    try {
      utf8.decode(utf8.encode(text));
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  String toString() {
    return 'Chat{id: $id, ideabookId: $ideabookId, messageCount: ${messages.length}}';
  }
}
