import 'package:voji/utils/logger.dart';

/// Defines a rate limit rule for chat messages
class ChatRateLimit {
  /// Maximum number of messages allowed in the time period
  final int maxMessages;

  /// Time period in seconds
  final int periodSeconds;

  /// Human-readable description of the rate limit
  final String description;

  /// Creates a new ChatRateLimit
  const ChatRateLimit({
    required this.maxMessages,
    required this.periodSeconds,
    required this.description,
  });

  /// Check if the rate limit has been reached
  /// Returns true if the limit has been reached
  bool isLimitReached(List<DateTime> messageTimes) {
    if (messageTimes.isEmpty) {
      Logger.debug('Rate limit check: $description - No messages, limit not reached');
      return false;
    }

    // Get the current time
    final now = DateTime.now();

    // Calculate the start of the time window
    final windowStart = now.subtract(Duration(seconds: periodSeconds));

    // Log the window details
    Logger.debug('Rate limit window: ${windowStart.toIso8601String()} to ${now.toIso8601String()} (${periodSeconds}s)');

    // Count messages within the time window
    final messagesInWindow = messageTimes.where((time) => time.isAfter(windowStart)).toList();

    // Log the messages in window
    Logger.debug('Messages in window: ${messagesInWindow.length}/$maxMessages');
    if (messagesInWindow.isNotEmpty) {
      Logger.debug('First message in window: ${messagesInWindow.first.toIso8601String()}');
      Logger.debug('Last message in window: ${messagesInWindow.last.toIso8601String()}');

      // Log all messages in window for detailed debugging
      Logger.debug('All messages in window:');
      for (int i = 0; i < messagesInWindow.length; i++) {
        final time = messagesInWindow[i];
        final timeAgo = DateTime.now().difference(time);
        Logger.debug('  $i: ${time.toIso8601String()} (${_formatDuration(timeAgo)} ago)');
      }
    }

    // Return true if the limit has been reached
    final isReached = messagesInWindow.length >= maxMessages;
    Logger.debug('Rate limit check: $description - ${messagesInWindow.length}/$maxMessages messages in the last $periodSeconds seconds - Limit reached: $isReached');

    return isReached;
  }

  /// Calculate the next available chat time based on this rate limit
  /// Returns null if the limit has not been reached
  DateTime? getNextAvailableTime(List<DateTime> messageTimes) {
    Logger.debug('===== CALCULATING NEXT AVAILABLE TIME =====');

    if (messageTimes.isEmpty) {
      Logger.debug('No messages, no restriction needed');
      return null;
    }

    // Sort message times (newest first)
    final sortedTimes = List<DateTime>.from(messageTimes)
      ..sort((a, b) => b.compareTo(a));

    Logger.debug('Total messages: ${sortedTimes.length}');
    Logger.debug('Newest message: ${sortedTimes.first.toIso8601String()}');
    Logger.debug('Oldest message: ${sortedTimes.last.toIso8601String()}');

    // If we don't have enough messages to reach the limit, no restriction
    if (sortedTimes.length < maxMessages) {
      Logger.debug('Not enough messages to reach limit ($maxMessages), no restriction needed');
      return null;
    }

    // Get the oldest message that contributes to reaching the limit
    // This is the message at index (maxMessages - 1) in the sorted list
    final oldestRelevantMessage = sortedTimes[maxMessages - 1];
    Logger.debug('Oldest relevant message (index ${maxMessages - 1}): ${oldestRelevantMessage.toIso8601String()}');

    // The next available time is when this message falls out of the window
    final nextAvailable = oldestRelevantMessage.add(Duration(seconds: periodSeconds));

    // Calculate how long until next available
    final now = DateTime.now();
    final timeUntilAvailable = nextAvailable.difference(now);

    Logger.debug('Next available time: ${nextAvailable.toIso8601String()} (in ${timeUntilAvailable.inSeconds}s)');
    Logger.debug('==========================================');

    return nextAvailable;
  }

  /// Format a duration for display in logs
  String _formatDuration(Duration duration) {
    if (duration.inSeconds < 60) {
      return '${duration.inSeconds}s';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    }
  }

  @override
  String toString() {
    return 'ChatRateLimit{maxMessages: $maxMessages, periodSeconds: $periodSeconds, description: $description}';
  }
}

/// Represents a log of chat message timestamps
class ChatMessageLog {
  /// List of timestamps when messages were sent
  final List<DateTime> messageTimes;

  /// When this log was last updated
  final DateTime updatedAt;

  /// Creates a new ChatMessageLog
  const ChatMessageLog({
    required this.messageTimes,
    required this.updatedAt,
  });

  /// Creates a copy of this ChatMessageLog with the given fields replaced with new values
  ChatMessageLog copyWith({
    List<DateTime>? messageTimes,
    DateTime? updatedAt,
  }) {
    return ChatMessageLog(
      messageTimes: messageTimes ?? this.messageTimes,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Add a new message timestamp to the log
  ChatMessageLog addMessage(DateTime timestamp) {
    final newMessageTimes = List<DateTime>.from(messageTimes)..add(timestamp);
    return copyWith(
      messageTimes: newMessageTimes,
      updatedAt: DateTime.now(),
    );
  }

  /// Remove messages older than the given TTL
  ChatMessageLog removeOldMessages(Duration ttl) {
    final cutoffTime = DateTime.now().subtract(ttl);
    final filteredTimes = messageTimes.where((time) => time.isAfter(cutoffTime)).toList();

    // Only create a new object if messages were actually removed
    if (filteredTimes.length < messageTimes.length) {
      return copyWith(
        messageTimes: filteredTimes,
        updatedAt: DateTime.now(),
      );
    }

    return this;
  }

  /// Convert this ChatMessageLog to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'messageTimes': messageTimes.map((time) => time.toIso8601String()).toList(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a ChatMessageLog from a JSON map
  factory ChatMessageLog.fromJson(Map<String, dynamic> json) {
    final List<dynamic> timeStrings = json['messageTimes'] as List<dynamic>;
    final List<DateTime> times = timeStrings
        .map((timeStr) => DateTime.parse(timeStr as String))
        .toList();

    return ChatMessageLog(
      messageTimes: times,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Create an empty ChatMessageLog
  factory ChatMessageLog.empty() {
    return ChatMessageLog(
      messageTimes: [],
      updatedAt: DateTime.now(),
    );
  }
}
