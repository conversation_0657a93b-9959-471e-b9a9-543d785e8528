import 'dart:convert';
import 'package:voji/utils/logger.dart';

/// Enum representing the role of a chat message
enum MessageRole {
  /// Message from the user
  user,

  /// Message from the assistant (<PERSON><PERSON>)
  assistant,
}

/// Represents a single message in a chat
class ChatMessage {
  /// Unique identifier for the message
  final String id;

  /// Role of the message sender (user or assistant)
  final MessageRole role;

  /// Content of the message
  final String content;

  /// When the message was created
  final DateTime timestamp;

  /// Additional metadata for the message (e.g., user_prompt for assistant messages)
  final Map<String, dynamic>? metadata;

  /// Whether this message has been saved as a note
  final bool isSavedAsNote;

  /// Creates a new ChatMessage instance
  const ChatMessage({
    required this.id,
    required this.role,
    required this.content,
    required this.timestamp,
    this.metadata,
    this.isSavedAsNote = false,
  });

  /// Creates a copy of this ChatMessage with the given fields replaced with new values
  ChatMessage copyWith({
    String? id,
    MessageRole? role,
    String? content,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
    bool? isSavedAsNote,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      role: role ?? this.role,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
      isSavedAsNote: isSavedAsNote ?? this.isSavedAsNote,
    );
  }

  /// Converts this ChatMessage to a JSON map
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'id': id,
      'role': role.toString().split('.').last,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'isSavedAsNote': isSavedAsNote,
    };

    // Add metadata if it exists
    if (metadata != null) {
      json['metadata'] = jsonEncode(metadata);
    }

    return json;
  }

  /// Creates a ChatMessage from a JSON map
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    // Ensure proper encoding of the content
    String content = json['content'] as String;
    try {
      content = _ensureProperEncoding(content);
    } catch (e) {
      Logger.error('Error ensuring proper encoding for chat message', e);
      // Continue with original content if encoding fails
    }

    // Extract metadata if it exists
    Map<String, dynamic>? metadata;
    if (json.containsKey('metadata')) {
      try {
        final metadataStr = json['metadata'] as String;
        metadata = jsonDecode(metadataStr) as Map<String, dynamic>;
      } catch (e) {
        Logger.error('Error decoding metadata for chat message', e);
        // Continue without metadata if decoding fails
      }
    }

    return ChatMessage(
      id: json['id'] as String,
      role: json['role'] == 'user' ? MessageRole.user : MessageRole.assistant,
      content: content,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: metadata,
      isSavedAsNote: json.containsKey('isSavedAsNote') ? json['isSavedAsNote'] as bool : false,
    );
  }

  /// Ensure proper encoding of a string
  static String _ensureProperEncoding(String content) {
    try {
      // First, check if the content is already valid UTF-8
      if (_isValidUtf8(content)) {
        return content;
      }

      // If not, try to decode and re-encode the content
      final decoded = utf8.decode(utf8.encode(content));
      return decoded;
    } catch (e) {
      Logger.error('Error ensuring proper encoding', e);
      return content; // Return original content if encoding fails
    }
  }

  /// Check if a string is valid UTF-8
  static bool _isValidUtf8(String text) {
    try {
      utf8.decode(utf8.encode(text));
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  String toString() {
    return 'ChatMessage{id: $id, role: $role, timestamp: $timestamp}';
  }
}
