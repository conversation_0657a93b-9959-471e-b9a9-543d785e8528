import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/utils/logger.dart';

/// Provider to track if debug mode is enabled
/// This is now always false since we're removing the debug mode toggle
/// We keep this provider to avoid breaking existing code that depends on it
final debugModeProvider = StateProvider<bool>((ref) {
  // Always false - debug mode is no longer toggleable
  Logger.debug('Debug mode initialized: false (debug mode toggle removed)');
  return false;
});
