import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/repositories/note_repository.dart';
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/ui/providers/firestore_note_provider.dart';
import 'package:voji/utils/logger.dart';

/// Provider for all notes for a specific ideabook
final ideabookNotesProvider = FutureProvider.family<List<Note>, String>((ref, ideabookId) async {
  // Watch the Firestore notifier provider
  final firestoreState = ref.watch(firestoreNotesNotifierProvider(ideabookId));

  // If the Firestore notifier has data, use it directly
  if (firestoreState is AsyncData<List<Note>>) {
    return firestoreState.value;
  }

  // If there's an error with Firestore, log it
  if (firestoreState is AsyncError) {
    Logger.error('Error getting notes from Firestore', firestoreState.error);
  }

  // Otherwise, fetch from repository
  final repository = ref.watch(noteRepositoryProvider);
  return repository.getNotesByIdeabookId(ideabookId);
});

/// Provider for a specific note by ID
/// Note: This provider requires the ideabook ID to be known in advance
final noteProvider = FutureProvider.family<Note?, Map<String, String>>((ref, params) async {
  final repository = ref.watch(noteRepositoryProvider);
  final ideabookId = params['ideabookId']!;
  final noteId = params['noteId']!;
  return repository.getNoteById(ideabookId, noteId);
});

/// Notifier for managing notes for a specific ideabook
class IdeabookNotesNotifier extends StateNotifier<AsyncValue<List<Note>>> {
  final NoteRepository _repository;
  final String _ideabookId;

  IdeabookNotesNotifier(this._repository, this._ideabookId) : super(const AsyncValue.loading()) {
    _loadNotes();
  }

  /// Load all notes for the ideabook from the database
  Future<void> _loadNotes() async {
    Logger.debug('Loading notes for ideabook $_ideabookId');
    try {
      state = const AsyncValue.loading();
      final notes = await _repository.getNotesByIdeabookId(_ideabookId);
      Logger.debug('Loaded ${notes.length} notes for ideabook $_ideabookId');
      state = AsyncValue.data(notes);
    } catch (e, stack) {
      Logger.error('Error loading notes for ideabook $_ideabookId', e);
      state = AsyncValue.error(e, stack);
    }
  }

  /// Refresh the notes list
  Future<void> refresh() async {
    await _loadNotes();
  }

  /// Create a new note
  Future<Note> createNote({
    required String title,
    required String content,
  }) async {
    Logger.debug('Creating new note for ideabook $_ideabookId');
    Logger.debug('IdeabookNotesNotifier: Note title received: "$title"');
    Logger.debug('IdeabookNotesNotifier: Note content length: ${content.length} characters');

    try {
      final newNote = await _repository.createNote(
        ideabookId: _ideabookId,
        title: title,
        content: content,
      );

      Logger.debug('New note created with ID: ${newNote.id}');

      // Refresh the list
      _loadNotes();

      return newNote;
    } catch (e) {
      Logger.error('Failed to create new note', e);
      rethrow;
    }
  }

  /// Update a note
  Future<bool> updateNote(Note note) async {
    final result = await _repository.updateNote(_ideabookId, note);

    // Refresh the list
    if (result) {
      _loadNotes();
    }

    return result;
  }

  /// Delete a note
  Future<bool> deleteNote(String id) async {
    final result = await _repository.deleteNote(_ideabookId, id);

    // Refresh the list
    if (result) {
      _loadNotes();
    }

    return result;
  }
}

/// Provider for the notes notifier for a specific ideabook
final ideabookNotesNotifierProvider = StateNotifierProvider.family<IdeabookNotesNotifier, AsyncValue<List<Note>>, String>((ref, ideabookId) {
  final repository = ref.watch(noteRepositoryProvider);
  return IdeabookNotesNotifier(repository, ideabookId);
});
