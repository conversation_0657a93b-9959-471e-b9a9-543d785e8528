import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/constants.dart';
import 'package:voji/models/note.dart';
import 'package:voji/services/firebase/firestore_service.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';

/// Notifier for managing notes with Firestore real-time updates
class FirestoreNotesNotifier extends StateNotifier<AsyncValue<List<Note>>> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Ideabook ID
  final String _ideabookId;

  /// Subscription to Firestore updates
  StreamSubscription<List<Note>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Flag to track if we should skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering notes
  bool _skipNextUpdate = false;

  /// Constructor
  FirestoreNotesNotifier(this._firestoreService, this._ideabookId) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore notes updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Starting to listen to Firestore notes updates for ideabook $_ideabookId');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Listen to Firestore updates
      _subscription = _firestoreService.listenToNotes(_ideabookId).listen(
        (notes) {
          Logger.debug('Received ${notes.length} notes from Firestore for ideabook $_ideabookId');

          // If we should skip this update, log it and reset the flag
          if (_skipNextUpdate) {
            Logger.debug('Skipping UI update for notes in ideabook $_ideabookId (optimistic update already applied)');
            _skipNextUpdate = false;
            return;
          }

          state = AsyncValue.data(notes);
        },
        onError: (error, stackTrace) {
          Logger.error('Error listening to Firestore notes for ideabook $_ideabookId', error);
          state = AsyncValue.error(error, stackTrace);
        },
      );
    } catch (e, stack) {
      Logger.error('Error setting up Firestore notes listener for ideabook $_ideabookId', e);
      state = AsyncValue.error(e, stack);
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore notes updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Stopping Firestore notes listener for ideabook $_ideabookId');
    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  /// Set the flag to skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering notes
  void skipNextUpdate() {
    Logger.debug('Setting flag to skip next Firestore update for ideabook $_ideabookId');
    _skipNextUpdate = true;
  }

  /// Check if the ideabook has reached the maximum number of notes
  Future<bool> isIdeabookFull() async {
    Logger.debug('FirestoreNotesNotifier: Checking if ideabook $_ideabookId is full of notes');

    try {
      // Get the current state of notes
      final currentState = state;
      if (currentState is AsyncData<List<Note>>) {
        final noteCount = currentState.value.length;
        final isFull = noteCount >= kMaxNotesPerIdeabook;
        Logger.debug('FirestoreNotesNotifier: Ideabook $_ideabookId has $noteCount notes, max is $kMaxNotesPerIdeabook, is full: $isFull');
        return isFull;
      } else if (currentState is AsyncLoading) {
        // If we're still loading, fetch the notes directly
        Logger.debug('FirestoreNotesNotifier: State is loading, fetching notes directly');
        final notes = await _firestoreService.listenToNotes(_ideabookId).first;
        final noteCount = notes.length;
        final isFull = noteCount >= kMaxNotesPerIdeabook;
        Logger.debug('FirestoreNotesNotifier: Ideabook $_ideabookId has $noteCount notes, max is $kMaxNotesPerIdeabook, is full: $isFull');
        return isFull;
      }

      // If we can't determine the count, assume not full to avoid blocking the user
      Logger.debug('FirestoreNotesNotifier: Could not determine note count, assuming not full');
      return false;
    } catch (e) {
      Logger.error('FirestoreNotesNotifier: Error checking if ideabook is full', e);
      // In case of error, assume not full to avoid blocking the user
      return false;
    }
  }

  /// Create a new note
  Future<Note> createNote({
    required String title,
    required String content,
  }) async {
    Logger.debug('FirestoreNotesNotifier: Creating new note for ideabook $_ideabookId');
    Logger.debug('FirestoreNotesNotifier: Note title received: "$title"');
    Logger.debug('FirestoreNotesNotifier: Note content length: ${content.length} characters');

    try {
      // Check if the ideabook is full of notes
      final isFull = await isIdeabookFull();
      if (isFull) {
        Logger.error('FirestoreNotesNotifier: Cannot create note - ideabook $_ideabookId is full');
        throw Exception('Ideabook is full. Maximum of $kMaxNotesPerIdeabook notes reached.');
      }

      // Create a new note with a timestamp-based ID
      // Use placeholder DateTime for createdAt since it will be set by Firestore's serverTimestamp()
      final now = DateTime.now(); // Only used for updatedAt
      final note = Note(
        id: IdUtils.generateId(),
        title: title,
        content: content,
        createdAt: now, // This will be replaced by server timestamp ('r' field)
        updatedAt: now,
      );

      // Create the note in Firestore
      final newNote = await _firestoreService.createNote(_ideabookId, note);
      Logger.debug('FirestoreNotesNotifier: Created new note with ID: ${newNote.id}');

      // No need to refresh the list as the Firestore listener will automatically update the state
      // when the document is added to Firestore

      return newNote;
    } catch (e) {
      Logger.error('FirestoreNotesNotifier: Error creating new note', e);
      // Rethrow the error so the UI can handle it appropriately
      // This ensures we don't create a note in Firestore when there's a failure
      rethrow;
    }
  }

  /// Update a note
  Future<bool> updateNote(Note note) async {
    Logger.debug('FirestoreNotesNotifier: Updating note in Firestore: ${note.id}');

    try {
      // Update the note in Firestore
      final result = await _firestoreService.updateNote(_ideabookId, note);
      Logger.debug('FirestoreNotesNotifier: Updated note in Firestore: ${note.id}, result: $result');

      // No need to refresh the list as the Firestore listener will automatically update the state
      // when the document changes in Firestore

      return result;
    } catch (e) {
      Logger.error('FirestoreNotesNotifier: Error updating note in Firestore', e);
      rethrow;
    }
  }

  /// Update the sort order of a note
  /// This is used when reordering notes via drag-and-drop
  Future<bool> updateNoteSortOrder(String noteId, double newSortOrder) async {
    Logger.debug('FirestoreNotesNotifier: Updating sort order for note $noteId to $newSortOrder');

    try {
      // First, get the current state of notes
      final currentState = state;
      if (currentState is AsyncData<List<Note>>) {
        // Find the note in the current list
        final currentNotes = currentState.value;
        final noteIndex = currentNotes.indexWhere((note) => note.id == noteId);

        if (noteIndex >= 0) {
          // Create a copy of the note with the updated sort order
          final note = currentNotes[noteIndex];
          final updatedNote = note.copyWith(sortOrder: newSortOrder);

          // Create a new list with the updated note
          final updatedNotes = List<Note>.from(currentNotes);
          updatedNotes[noteIndex] = updatedNote;

          // Sort the list according to the new order
          updatedNotes.sort((a, b) {
            final aValue = a.getEffectiveSortValue();
            final bValue = b.getEffectiveSortValue();
            return bValue.compareTo(aValue); // Descending order
          });

          // Update the state immediately with the new order
          state = AsyncData(updatedNotes);

          // Skip the next update from Firestore since we've already updated the UI
          skipNextUpdate();

          // Now update Firestore
          final result = await _firestoreService.updateNoteSortOrder(_ideabookId, noteId, newSortOrder);
          Logger.debug('FirestoreNotesNotifier: Updated sort order for note $noteId, result: $result');

          return result;
        }
      }

      // If we couldn't update the state optimistically, just update Firestore
      final result = await _firestoreService.updateNoteSortOrder(_ideabookId, noteId, newSortOrder);
      Logger.debug('FirestoreNotesNotifier: Updated sort order for note $noteId, result: $result');

      return result;
    } catch (e) {
      Logger.error('FirestoreNotesNotifier: Error updating sort order for note $noteId', e);
      rethrow;
    }
  }

  /// Delete a note
  Future<bool> deleteNote(String noteId) async {
    Logger.debug('FirestoreNotesNotifier: Deleting note from Firestore: $noteId');

    try {
      // Delete the note from Firestore
      final result = await _firestoreService.deleteNote(_ideabookId, noteId);
      Logger.debug('FirestoreNotesNotifier: Deleted note from Firestore: $noteId, result: $result');

      // No need to refresh the list as the Firestore listener will automatically update the state
      // when the document is removed from Firestore

      return result;
    } catch (e) {
      Logger.error('FirestoreNotesNotifier: Error deleting note from Firestore', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore notes notifier
final firestoreNotesNotifierProvider = StateNotifierProvider.family<FirestoreNotesNotifier, AsyncValue<List<Note>>, String>((ref, ideabookId) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return FirestoreNotesNotifier(firestoreService, ideabookId);
});
