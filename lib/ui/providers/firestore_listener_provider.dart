import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/firebase/firestore_error_handler.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';

/// Provider for managing Firestore ideabooks listener with the global pool
class FirestoreIdeabooksListenerNotifier extends StateNotifier<AsyncValue<List<Ideabook>>> {
  /// Firestore listener pool
  final FirestoreListenerPool _listenerPool;

  /// Reference to the provider container
  final Ref _ref;

  /// Subscription to Firestore updates
  StreamSubscription<List<Ideabook>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Constructor
  FirestoreIdeabooksListenerNotifier(this._listenerPool, this._ref) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore ideabooks updates');
      return;
    }

    Logger.debug('Starting to listen to Firestore ideabooks updates using listener pool');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Get the stream from the listener pool
      final stream = _listenerPool.getIdeabooksStream();

      // Listen to Firestore updates
      _subscription = stream.listen(
        (ideabooks) {
          Logger.debug('Received ${ideabooks.length} ideabooks from Firestore listener pool');
          state = AsyncValue.data(ideabooks);
        },
        onError: (error, stackTrace) {
          // Check if this is a permission error
          final isPermissionError = ErrorUtils.isPermissionError(error);

          if (isPermissionError) {
            Logger.error('Permission error listening to Firestore ideabooks', error);
            Logger.debug('Stopping listener due to permission error - user may have changed');

            // Stop listening to avoid continuous permission errors
            stopListening();

            // Set error state with generic message
            state = AsyncValue.error(
              ErrorUtils.permissionDeniedMessage,
              stackTrace
            );

            // Handle the permission error globally
            ErrorUtils.handleGlobalPermissionError(_ref, error);
          } else {
            // Handle other errors
            Logger.error('Error listening to Firestore ideabooks', error);
            state = AsyncValue.error(error, stackTrace);
          }
        },
      );
    } catch (e, stack) {
      // Check if this is a permission error
      final isPermissionError = ErrorUtils.isPermissionError(e);

      if (isPermissionError) {
        Logger.error('Permission error setting up Firestore ideabooks listener', e);
        Logger.debug('User may have changed accounts - permission error when setting up listener');

        // Set error state with generic message
        state = AsyncValue.error(
          ErrorUtils.permissionDeniedMessage,
          stack
        );

        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      } else {
        // Handle other errors
        Logger.error('Error setting up Firestore ideabooks listener', e);
        state = AsyncValue.error(e, stack);
      }
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore ideabooks updates');
      return;
    }

    Logger.debug('Stopping Firestore ideabooks listener');
    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore ideabooks listener notifier
final firestoreIdeabooksListenerNotifierProvider = StateNotifierProvider<FirestoreIdeabooksListenerNotifier, AsyncValue<List<Ideabook>>>((ref) {
  final listenerPool = ref.watch(firestoreListenerPoolProvider);
  return FirestoreIdeabooksListenerNotifier(listenerPool, ref);
});

/// Provider for managing Firestore ideas listener with the global pool
class FirestoreIdeasListenerNotifier extends StateNotifier<AsyncValue<List<Idea>>> {
  /// Firestore listener pool
  final FirestoreListenerPool _listenerPool;

  /// Ideabook ID
  final String _ideabookId;

  /// Reference to the provider container
  final Ref _ref;

  /// Subscription to Firestore updates
  StreamSubscription<List<Idea>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Flag to track if we should skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering ideas
  bool _skipNextUpdate = false;

  /// Constructor
  FirestoreIdeasListenerNotifier(this._listenerPool, this._ideabookId, this._ref) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore ideas updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Starting to listen to Firestore ideas updates for ideabook $_ideabookId using listener pool');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Get the stream from the listener pool
      final stream = _listenerPool.getIdeasStream(_ideabookId);

      // Listen to Firestore updates
      _subscription = stream.listen(
        (ideas) {
          Logger.debug('Received ${ideas.length} ideas from Firestore listener pool for ideabook $_ideabookId');

          // If we should skip this update, log it and reset the flag
          if (_skipNextUpdate) {
            Logger.debug('Skipping UI update for ideas in ideabook $_ideabookId (optimistic update already applied)');
            _skipNextUpdate = false;
            return;
          }

          state = AsyncValue.data(ideas);
        },
        onError: (error, stackTrace) {
          // Check if this is a permission error
          final isPermissionError = ErrorUtils.isPermissionError(error);

          if (isPermissionError) {
            Logger.error('Permission error listening to Firestore ideas for ideabook $_ideabookId', error);
            Logger.debug('Stopping listener due to permission error - user may have changed');

            // Stop listening to avoid continuous permission errors
            stopListening();

            // Set error state with generic message
            state = AsyncValue.error(
              ErrorUtils.permissionDeniedMessage,
              stackTrace
            );

            // Handle the permission error globally
            ErrorUtils.handleGlobalPermissionError(_ref, error);
          } else {
            // Handle other errors
            Logger.error('Error listening to Firestore ideas for ideabook $_ideabookId', error);
            state = AsyncValue.error(error, stackTrace);
          }
        },
      );
    } catch (e, stack) {
      // Check if this is a permission error
      final isPermissionError = ErrorUtils.isPermissionError(e);

      if (isPermissionError) {
        Logger.error('Permission error setting up Firestore ideas listener for ideabook $_ideabookId', e);
        Logger.debug('User may have changed accounts - permission error when setting up listener');

        // Set error state with generic message
        state = AsyncValue.error(
          ErrorUtils.permissionDeniedMessage,
          stack
        );

        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      } else {
        // Handle other errors
        Logger.error('Error setting up Firestore ideas listener for ideabook $_ideabookId', e);
        state = AsyncValue.error(e, stack);
      }

      // Make sure we're not in listening state
      _isListening = false;
    }
  }

  /// Set the flag to skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering ideas
  void skipNextUpdate() {
    Logger.debug('Setting flag to skip next Firestore update for ideabook $_ideabookId in listener pool');
    _skipNextUpdate = true;
  }

  /// Update the state with a reordered list of ideas
  /// This is used to immediately reflect reordering in the UI
  void updateWithReorderedIdeas(String ideaId, double newSortOrder) {
    Logger.debug('Updating state with reordered ideas for ideabook $_ideabookId');

    // Only update if we have data
    final currentState = state;
    if (currentState is AsyncData<List<Idea>>) {
      // Find the idea in the current list
      final currentIdeas = currentState.value;
      final ideaIndex = currentIdeas.indexWhere((idea) => idea.id == ideaId);

      if (ideaIndex >= 0) {
        // Create a copy of the idea with the updated sort order
        final idea = currentIdeas[ideaIndex];
        final updatedIdea = idea.copyWith(sortOrder: newSortOrder);

        // Create a new list with the updated idea
        final updatedIdeas = List<Idea>.from(currentIdeas);
        updatedIdeas[ideaIndex] = updatedIdea;

        // Sort the list according to the new order
        updatedIdeas.sort((a, b) {
          final aValue = a.getEffectiveSortValue();
          final bValue = b.getEffectiveSortValue();
          return bValue.compareTo(aValue); // Descending order
        });

        // Update the state immediately with the new order
        state = AsyncData(updatedIdeas);

        // Skip the next update from Firestore since we've already updated the UI
        skipNextUpdate();

        Logger.debug('Successfully updated state with reordered ideas');
      }
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore ideas updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Stopping Firestore ideas listener for ideabook $_ideabookId');
    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore ideas listener notifier
final firestoreIdeasListenerNotifierProvider = StateNotifierProvider.family<FirestoreIdeasListenerNotifier, AsyncValue<List<Idea>>, String>((ref, ideabookId) {
  final listenerPool = ref.watch(firestoreListenerPoolProvider);
  return FirestoreIdeasListenerNotifier(listenerPool, ideabookId, ref);
});

/// Provider for managing Firestore notes listener with the global pool
class FirestoreNotesListenerNotifier extends StateNotifier<AsyncValue<List<Note>>> {
  /// Firestore listener pool
  final FirestoreListenerPool _listenerPool;

  /// Ideabook ID
  final String _ideabookId;

  /// Reference to the provider container
  final Ref _ref;

  /// Subscription to Firestore updates
  StreamSubscription<List<Note>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Flag to track if we should skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering notes
  bool _skipNextUpdate = false;

  /// Constructor
  FirestoreNotesListenerNotifier(this._listenerPool, this._ideabookId, this._ref) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore notes updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Starting to listen to Firestore notes updates for ideabook $_ideabookId using listener pool');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Get the stream from the listener pool
      final stream = _listenerPool.getNotesStream(_ideabookId);

      // Listen to Firestore updates
      _subscription = stream.listen(
        (notes) {
          Logger.debug('Received ${notes.length} notes from Firestore listener pool for ideabook $_ideabookId');

          // If we should skip this update, log it and reset the flag
          if (_skipNextUpdate) {
            Logger.debug('Skipping UI update for notes in ideabook $_ideabookId (optimistic update already applied)');
            _skipNextUpdate = false;
            return;
          }

          state = AsyncValue.data(notes);
        },
        onError: (error, stackTrace) {
          // Check if this is a permission error
          final isPermissionError = ErrorUtils.isPermissionError(error);

          if (isPermissionError) {
            Logger.error('Permission error listening to Firestore notes for ideabook $_ideabookId', error);
            Logger.debug('Stopping listener due to permission error - user may have changed');

            // Stop listening to avoid continuous permission errors
            stopListening();

            // Set error state with generic message
            state = AsyncValue.error(
              ErrorUtils.permissionDeniedMessage,
              stackTrace
            );

            // Handle the permission error globally
            ErrorUtils.handleGlobalPermissionError(_ref, error);
          } else {
            // Handle other errors
            Logger.error('Error listening to Firestore notes for ideabook $_ideabookId', error);
            state = AsyncValue.error(error, stackTrace);
          }
        },
      );
    } catch (e, stack) {
      // Check if this is a permission error
      final isPermissionError = ErrorUtils.isPermissionError(e);

      if (isPermissionError) {
        Logger.error('Permission error setting up Firestore notes listener for ideabook $_ideabookId', e);
        Logger.debug('User may have changed accounts - permission error when setting up listener');

        // Set error state with generic message
        state = AsyncValue.error(
          ErrorUtils.permissionDeniedMessage,
          stack
        );

        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      } else {
        // Handle other errors
        Logger.error('Error setting up Firestore notes listener for ideabook $_ideabookId', e);
        state = AsyncValue.error(e, stack);
      }
    }
  }

  /// Set the flag to skip the next update from Firestore
  /// This is used to prevent UI flashing when reordering notes
  void skipNextUpdate() {
    Logger.debug('Setting flag to skip next Firestore update for ideabook $_ideabookId in listener pool');
    _skipNextUpdate = true;
  }

  /// Update the state with a reordered list of notes
  /// This is used to immediately reflect reordering in the UI
  void updateWithReorderedNotes(String noteId, double newSortOrder) {
    Logger.debug('Updating state with reordered notes for ideabook $_ideabookId');

    // Only update if we have data
    final currentState = state;
    if (currentState is AsyncData<List<Note>>) {
      // Find the note in the current list
      final currentNotes = currentState.value;
      final noteIndex = currentNotes.indexWhere((note) => note.id == noteId);

      if (noteIndex >= 0) {
        // Create a copy of the note with the updated sort order
        final note = currentNotes[noteIndex];
        final updatedNote = note.copyWith(sortOrder: newSortOrder);

        // Create a new list with the updated note
        final updatedNotes = List<Note>.from(currentNotes);
        updatedNotes[noteIndex] = updatedNote;

        // Sort the list according to the new order
        updatedNotes.sort((a, b) {
          final aValue = a.getEffectiveSortValue();
          final bValue = b.getEffectiveSortValue();
          return bValue.compareTo(aValue); // Descending order
        });

        // Update the state immediately with the new order
        state = AsyncData(updatedNotes);

        // Skip the next update from Firestore since we've already updated the UI
        skipNextUpdate();

        Logger.debug('Successfully updated state with reordered notes');
      }
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore notes updates for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Stopping Firestore notes listener for ideabook $_ideabookId');
    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore notes listener notifier
final firestoreNotesListenerNotifierProvider = StateNotifierProvider.family<FirestoreNotesListenerNotifier, AsyncValue<List<Note>>, String>((ref, ideabookId) {
  final listenerPool = ref.watch(firestoreListenerPoolProvider);
  return FirestoreNotesListenerNotifier(listenerPool, ideabookId, ref);
});
