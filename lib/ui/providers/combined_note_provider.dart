import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/note.dart';
import 'package:voji/ui/providers/firestore_listener_provider.dart';
import 'package:voji/utils/logger.dart';

/// Provider for notes using Firestore with optimized listener pool
final combinedNotesNotifierProvider = Provider.family<StateNotifierProvider<StateNotifier<AsyncValue<List<Note>>>, AsyncValue<List<Note>>>, String>((ref, ideabookId) {
  Logger.debug('Using Firestore with optimized listener pool for notes in ideabook $ideabookId');
  return firestoreNotesListenerNotifierProvider(ideabookId);
});

/// Provider for notes from Firestore with optimized listener pool
final combinedNotesProvider = FutureProvider.family<List<Note>, String>((ref, ideabookId) async {
  // Get notes from Firestore
  try {
    final firestoreState = ref.watch(firestoreNotesListenerNotifierProvider(ideabookId));

    if (firestoreState is AsyncData<List<Note>>) {
      return firestoreState.value;
    }

    // If Firestore is loading or has an error, return an empty list
    if (firestoreState is AsyncLoading) {
      Logger.debug('Firestore notes are still loading for ideabook $ideabookId');
      return [];
    }

    if (firestoreState is AsyncError) {
      Logger.error('Error getting notes from Firestore', firestoreState.error);
      return [];
    }

    return [];
  } catch (e) {
    Logger.error('Error in combinedNotesProvider with Firestore', e);
    return [];
  }
});
