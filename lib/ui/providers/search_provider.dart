import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/common/filter_provider.dart';

/// Provider for the search query
final searchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for the ideabook search filter
final ideabookSearchFilterProvider = Provider<FilterProvider<Ideabook>>((ref) {
  final query = ref.watch(searchQueryProvider);
  final filter = FilterProvider<Ideabook>();

  // Add a string contains filter criterion for the ideabook name
  filter.addCriterion(
    StringContainsFilterCriterion<Ideabook>(
      query: query,
      stringExtractor: (ideabook) => ideabook.name,
    ),
  );

  return filter;
});

/// Provider for filtered ideabooks based on search query
/// This maintains backward compatibility with existing code
final filteredIdeabooksProvider = Provider.family<List<Ideabook>, List<Ideabook>>(
  (ref, ideabooks) {
    final filter = ref.watch(ideabookSearchFilterProvider);
    return filter.apply(ideabooks);
  },
);

/// Provider for search state
final isSearchingProvider = StateProvider<bool>((ref) => false);
