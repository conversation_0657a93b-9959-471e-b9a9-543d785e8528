import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/firebase/firestore_service.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';
import 'package:voji/models/enums.dart';
import 'package:voji/models/ideabook.dart';

/// Notifier for managing ideabooks with Firestore real-time updates
class FirestoreIdeabooksNotifier extends StateNotifier<AsyncValue<List<Ideabook>>> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Subscription to Firestore updates
  StreamSubscription<List<Ideabook>>? _subscription;

  /// Whether the notifier is currently listening to Firestore updates
  bool _isListening = false;

  /// Constructor
  FirestoreIdeabooksNotifier(this._firestoreService) : super(const AsyncValue.loading()) {
    // Initial load is done when startListening is called
  }

  /// Start listening to Firestore updates
  void startListening() {
    if (_isListening) {
      Logger.debug('Already listening to Firestore ideabooks updates');
      return;
    }

    Logger.debug('Starting to listen to Firestore ideabooks updates');
    _isListening = true;

    try {
      state = const AsyncValue.loading();

      // Listen to Firestore updates
      _subscription = _firestoreService.listenToIdeabooks().listen(
        (ideabooks) {
          Logger.debug('Received ${ideabooks.length} ideabooks from Firestore');
          state = AsyncValue.data(ideabooks);
        },
        onError: (error, stackTrace) {
          Logger.error('Error listening to Firestore ideabooks', error);
          state = AsyncValue.error(error, stackTrace);
        },
      );
    } catch (e, stack) {
      Logger.error('Error setting up Firestore ideabooks listener', e);
      state = AsyncValue.error(e, stack);
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!_isListening) {
      Logger.debug('Not listening to Firestore ideabooks updates');
      return;
    }

    Logger.debug('Stopping Firestore ideabooks listener');
    _subscription?.cancel();
    _subscription = null;
    _isListening = false;
  }

  /// Create a new ideabook
  Future<Ideabook> createIdeabook({
    required String name,
    IdeabookColor color = IdeabookColor.none,
    bool isLocked = false,
    Function? onCreated,
  }) async {
    Logger.debug('Creating ideabook in Firestore: $name');

    try {
      // Validate name length (maximum 100 characters)
      if (name.length > maxIdeabookNameLength) {
        // Truncate the name to 100 characters
        name = name.substring(0, maxIdeabookNameLength);
        Logger.debug('Ideabook name truncated to $maxIdeabookNameLength characters: $name');
      }

      // Create the ideabook model
      final now = DateTime.now();
      final ideabook = Ideabook(
        id: IdUtils.generateId(), // This ID will be replaced by Firestore
        name: name,
        color: color,
        isLocked: isLocked,
        createdAt: now,
        updatedAt: now,
      );

      // Create the ideabook in Firestore
      final newIdeabook = await _firestoreService.createIdeabook(ideabook);
      Logger.debug('Created ideabook in Firestore: ${newIdeabook.id}');

      // Call the callback if provided
      if (onCreated != null) {
        onCreated();
      }

      return newIdeabook;
    } catch (e) {
      Logger.error('Error creating ideabook in Firestore', e);
      rethrow;
    }
  }

  /// Update an ideabook
  Future<bool> updateIdeabook(Ideabook ideabook) async {
    Logger.debug('Updating ideabook in Firestore: ${ideabook.id}');

    try {
      // Update the ideabook in Firestore
      final result = await _firestoreService.updateIdeabook(ideabook);
      Logger.debug('Updated ideabook in Firestore: ${ideabook.id}, result: $result');
      return result;
    } catch (e) {
      Logger.error('Error updating ideabook in Firestore', e);
      rethrow;
    }
  }

  /// Delete an ideabook
  Future<bool> deleteIdeabook(String id) async {
    Logger.debug('Deleting ideabook from Firestore: $id');

    try {
      // Delete the ideabook from Firestore
      final result = await _firestoreService.deleteIdeabook(id);
      Logger.debug('Deleted ideabook from Firestore: $id, result: $result');
      return result;
    } catch (e) {
      Logger.error('Error deleting ideabook from Firestore', e);
      rethrow;
    }
  }

  /// Update an ideabook's color
  Future<bool> updateIdeabookColor(String ideabookId, IdeabookColor newColor) async {
    Logger.debug('Updating ideabook color in Firestore: $ideabookId to ${newColor.name} (index: ${newColor.index})');

    try {
      // Get the current state
      if (state is! AsyncData<List<Ideabook>>) {
        Logger.debug('State is not AsyncData<List<Ideabook>>, cannot update color');
        return false;
      }

      // Find the ideabook in the current state
      final ideabooks = (state as AsyncData<List<Ideabook>>).value;
      final ideabook = ideabooks.firstWhere(
        (ideabook) => ideabook.id == ideabookId,
        orElse: () => throw Exception('Ideabook not found'),
      );

      Logger.debug('Found ideabook: ${ideabook.id}, current color: ${ideabook.color.name} (index: ${ideabook.color.index})');

      // Update the ideabook with the new color
      final updatedIdeabook = ideabook.copyWith(
        color: newColor,
        updatedAt: DateTime.now(),
      );

      Logger.debug('Created updated ideabook with new color: ${updatedIdeabook.color.name} (index: ${updatedIdeabook.color.index})');

      // Update the ideabook in Firestore
      final result = await updateIdeabook(updatedIdeabook);
      Logger.debug('Update result: $result');
      return result;
    } catch (e) {
      Logger.error('Error updating ideabook color in Firestore', e);
      rethrow;
    }
  }

  /// Toggle an ideabook's lock state
  Future<bool> toggleIdeabookLock(String ideabookId) async {
    Logger.debug('Toggling ideabook lock state in Firestore: $ideabookId');
    Logger.debug('FirestoreIdeabooksNotifier: Current state type: ${state.runtimeType}');

    try {
      // If state is loading, wait for data to be available
      if (state is AsyncLoading) {
        Logger.debug('FirestoreIdeabooksNotifier: State is AsyncLoading, waiting for data...');

        // Create a completer to wait for data
        final completer = Completer<bool>();

        // Set up a temporary listener to wait for data
        late StreamSubscription<AsyncValue<List<Ideabook>>> subscription;

        subscription = stream.listen((newState) {
          if (newState is AsyncData<List<Ideabook>> && !completer.isCompleted) {
            Logger.debug('FirestoreIdeabooksNotifier: Data loaded, proceeding with toggle');
            subscription.cancel();
            completer.complete(true);
          } else if (newState is AsyncError && !completer.isCompleted) {
            Logger.error('FirestoreIdeabooksNotifier: Error loading data');
            subscription.cancel();
            completer.complete(false);
          }
        });

        // Add a timeout to avoid waiting forever
        Future.delayed(const Duration(seconds: 5), () {
          if (!completer.isCompleted) {
            Logger.error('FirestoreIdeabooksNotifier: Timeout waiting for data');
            subscription.cancel();
            completer.complete(false);
          }
        });

        // Wait for data or timeout
        final success = await completer.future;
        if (!success) {
          return false;
        }
      }

      // Check state again after potential loading
      if (state is! AsyncData<List<Ideabook>>) {
        Logger.error('FirestoreIdeabooksNotifier: Cannot toggle lock state - state is not AsyncData after waiting');

        // Ensure we're listening to updates
        if (!_isListening) {
          startListening();
        }

        return false;
      }

      // Now we should have AsyncData
      final ideabooks = (state as AsyncData<List<Ideabook>>).value;

      // Find the ideabook
      Ideabook? ideabook;
      try {
        ideabook = ideabooks.firstWhere(
          (book) => book.id == ideabookId,
          orElse: () => throw Exception('Ideabook not found'),
        );
      } catch (e) {
        Logger.error('FirestoreIdeabooksNotifier: Error finding ideabook', e);
        return false;
      }

      Logger.debug('FirestoreIdeabooksNotifier: Found ideabook: ${ideabook.id}, current lock state: ${ideabook.isLocked}');

      // Update the ideabook with the toggled lock state
      final updatedIdeabook = ideabook.copyWith(
        isLocked: !ideabook.isLocked,
        updatedAt: DateTime.now(),
      );

      Logger.debug('FirestoreIdeabooksNotifier: Created updated ideabook with new lock state: ${updatedIdeabook.isLocked}');

      // Update the ideabook in Firestore
      return updateIdeabook(updatedIdeabook);
    } catch (e) {
      Logger.error('Error toggling ideabook lock state in Firestore', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}

/// Provider for the Firestore ideabooks notifier
final firestoreIdeabooksNotifierProvider = StateNotifierProvider<FirestoreIdeabooksNotifier, AsyncValue<List<Ideabook>>>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return FirestoreIdeabooksNotifier(firestoreService);
});
