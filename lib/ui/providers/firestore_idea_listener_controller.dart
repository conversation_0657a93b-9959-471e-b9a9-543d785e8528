import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/auth/auth_providers.dart';
import 'package:voji/ui/providers/firestore_idea_provider.dart';
import 'package:voji/ui/providers/firestore_listener_provider.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';

/// Provider to control Firestore ideas listeners
/// This provider is used to start and stop Firestore ideas listeners
/// when navigating to and from the ideas tab
class FirestoreIdeasListenerController extends StateNotifier<bool> {
  final Ref _ref;
  final String _ideabookId;

  FirestoreIdeasListenerController(this._ref, this._ideabookId) : super(false);

  /// Start listening to Firestore updates
  void startListening() {
    if (state) {
      Logger.debug('Firestore ideas listener already active for ideabook $_ideabookId');
      return;
    }

    Logger.debug('Starting Firestore ideas listener for ideabook $_ideabookId with optimized listener pool');

    try {
      // Get the current user ID for debugging
      final currentUser = _ref.read(firebaseUserProvider).value;
      final userId = currentUser?.uid;
      Logger.debug('Current user ID when starting ideas listener: ${userId ?? 'null'}');

      // Start listening
      final notifier = _ref.read(firestoreIdeasListenerNotifierProvider(_ideabookId).notifier);
      notifier.startListening();

      // Listen for errors in the ideas provider
      _ref.listen(firestoreIdeasListenerNotifierProvider(_ideabookId), (previous, current) {
        if (current is AsyncError) {
          // Check if this is a permission error
          final isPermissionError = current.error != null && ErrorUtils.isPermissionError(current.error!);

          if (isPermissionError) {
            Logger.error('Permission error detected in ideas listener controller', current.error);
            // Force stop listening to avoid continuous permission errors
            try {
              // Call directly to the notifier to stop listening
              final notifier = _ref.read(firestoreIdeasListenerNotifierProvider(_ideabookId).notifier);
              notifier.stopListening();

              // Update our state
              state = false;

              Logger.debug('Successfully stopped listener due to permission error');

              // Handle the permission error globally
              ErrorUtils.handleGlobalPermissionError(_ref, current.error!);
            } catch (e) {
              Logger.error('Error stopping listener after permission error', e);
            }
          }
        }
      });

      state = true;
    } catch (e) {
      Logger.error('Error starting Firestore ideas listener', e);

      // Check if this is a permission error
      if (ErrorUtils.isPermissionError(e)) {
        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      }

      state = false;
    }
  }

  /// Update the sort order of an idea
  /// This is used when reordering ideas via drag-and-drop
  Future<bool> updateIdeaSortOrder(String ideaId, double newSortOrder) async {
    Logger.debug('FirestoreIdeasListenerController: Updating sort order for idea $ideaId to $newSortOrder');

    try {
      // Update the listener state immediately to reflect the reordering in the UI
      final listenerNotifier = _ref.read(firestoreIdeasListenerNotifierProvider(_ideabookId).notifier);
      listenerNotifier.updateWithReorderedIdeas(ideaId, newSortOrder);

      // Update the sort order in Firestore using the direct provider
      final firestoreNotifier = _ref.read(firestoreIdeasNotifierProvider(_ideabookId).notifier);
      final result = await firestoreNotifier.updateIdeaSortOrder(ideaId, newSortOrder);

      Logger.debug('FirestoreIdeasListenerController: Updated sort order for idea $ideaId, result: $result');
      return result;
    } catch (e) {
      Logger.error('FirestoreIdeasListenerController: Error updating sort order for idea $ideaId', e);
      return false;
    }
  }

  /// Stop listening to Firestore updates
  /// This method now properly stops the listener when there's a permission error
  /// but otherwise relies on the TTL-based cleanup for normal navigation
  void stopListening() {
    if (!state) {
      Logger.debug('Firestore ideas listener already inactive for ideabook $_ideabookId');
      return;
    }

    // Check if we're being called due to a permission error
    final stackTrace = StackTrace.current.toString();
    final isPermissionError = stackTrace.contains('permission error');

    if (isPermissionError) {
      // If this is a permission error, actually stop the listener
      Logger.debug('Stopping Firestore ideas listener for ideabook $_ideabookId due to permission error');
      try {
        final notifier = _ref.read(firestoreIdeasListenerNotifierProvider(_ideabookId).notifier);
        notifier.stopListening();
      } catch (e) {
        Logger.error('Error stopping Firestore ideas listener', e);
      }
    } else {
      // For normal navigation, don't stop the listener
      // The listener pool will handle TTL-based cleanup
      Logger.debug('Not stopping Firestore ideas listener for ideabook $_ideabookId - using listener pool TTL instead');
    }

    // Update the state to indicate we're not actively listening in this controller
    state = false;
  }
}

/// Provider for the Firestore ideas listener controller
final firestoreIdeasListenerControllerProvider = StateNotifierProvider.family<FirestoreIdeasListenerController, bool, String>((ref, ideabookId) {
  return FirestoreIdeasListenerController(ref, ideabookId);
});
