import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/firestore_listener_provider.dart';
import 'package:voji/utils/logger.dart';

/// Provider for ideas using Firestore with optimized listener pool
final combinedIdeasNotifierProvider = Provider.family<StateNotifierProvider<StateNotifier<AsyncValue<List<Idea>>>, AsyncValue<List<Idea>>>, String>((ref, ideabookId) {
  Logger.debug('Using Firestore with optimized listener pool for ideas in ideabook $ideabookId');
  return firestoreIdeasListenerNotifierProvider(ideabookId);
});
