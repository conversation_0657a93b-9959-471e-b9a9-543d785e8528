import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/chat.dart';
import 'package:voji/models/chat_message.dart';
import 'package:voji/models/idea.dart';
import 'package:voji/repositories/chat_file_repository.dart';
import 'package:voji/repositories/chat_repository_provider.dart';
import 'package:voji/services/llm/gemini_chat_service.dart' as gemini;
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/id_utils.dart';

/// Provider for a chat by ideabook ID
final chatProvider = FutureProvider.family<Chat?, String>((ref, ideabookId) async {
  final repository = ref.watch(chatRepositoryProvider);
  return repository.getChatByIdeabookId(ideabookId);
});

/// State class for chat
class ChatState {
  /// The chat messages
  final List<ChatMessage> messages;

  /// Whether a message is currently being sent
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Creates a new ChatState
  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  /// Creates a copy of this ChatState with the given fields replaced with new values
  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Creates a new ChatState with the error message cleared
  ChatState clearError() {
    return ChatState(
      messages: messages,
      isLoading: isLoading,
      errorMessage: null,
    );
  }
}

/// Notifier for managing chat state
class ChatNotifier extends StateNotifier<ChatState> {
  final ChatFileRepository _repository;
  final gemini.GeminiChatService _geminiChatService;
  final String _ideabookId;

  /// Constructor
  ChatNotifier(this._repository, this._geminiChatService, this._ideabookId) : super(const ChatState()) {
    _loadChat();
  }

  /// Load the chat from the database
  Future<void> _loadChat() async {
    try {
      // Get the chat from the database
      final chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat != null) {
        // Update the state with the loaded messages
        state = state.copyWith(messages: chat.messages);
      } else {
        // Create a new chat if none exists
        await _repository.createChat(ideabookId: _ideabookId);
      }
    } catch (e) {
      Logger.error('Error loading chat', e);
      state = state.copyWith(errorMessage: 'Error loading chat: $e');
    }
  }

  /// Send a message to the chat
  Future<void> sendMessage(String content, {required List<Idea> ideas, required String ideabookName}) async {
    if (content.trim().isEmpty) return;

    try {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // Create a new user message
      final userMessage = ChatMessage(
        id: IdUtils.generateId(),
        role: MessageRole.user,
        content: content,
        timestamp: DateTime.now(),
      );

      // Add the user message to the state
      state = state.copyWith(
        messages: [...state.messages, userMessage],
      );

      // Get the chat from the database
      var chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat == null) {
        // Create a new chat if none exists
        chat = await _repository.createChat(
          ideabookId: _ideabookId,
          messages: [userMessage],
        );
      } else {
        // Add the message to the existing chat
        await _repository.addMessageToChat(
          chat: chat,
          role: MessageRole.user,
          content: content,
        );

        // Update the chat with the new message
        chat = await _repository.getChatByIdeabookId(_ideabookId);
      }

      // EXTENSIVE DEBUGGING: Log detailed information about the ideas
      Logger.debug('======= CHAT DEBUGGING =======');
      Logger.debug('Ideabook ID: $_ideabookId, Name: $ideabookName');
      Logger.debug('Total ideas received: ${ideas.length}');

      // Log each idea individually for debugging
      if (ideas.isNotEmpty) {
        Logger.debug('Ideas details:');
        for (int i = 0; i < ideas.length; i++) {
          final idea = ideas[i];
          Logger.debug('  Idea $i - ID: ${idea.id}');
          // IdeabookId is now implicit from the collection structure
          Logger.debug('  Idea $i - Content: "${idea.content.substring(0, idea.content.length.clamp(0, 50))}${idea.content.length > 50 ? "..." : ""}"');
          Logger.debug('  Idea $i - CreatedAt: ${idea.createdAt}');
        }
      } else {
        Logger.debug('WARNING: No ideas were provided to the chat prompt!');
      }

      // No need to verify ideas belong to the correct ideabook anymore
      // Since ideabookId is now implicit from the collection structure
      // All ideas retrieved from the ideabook's subcollection already belong to that ideabook

      // Format the ideas for the prompt with date only (no time)
      final ideasText = ideas.isEmpty
          ? "No ideas found in this ideabook yet."
          : ideas.map((idea) {
              // Extract only the date part (YYYY-MM-DD)
              final dateOnly = idea.createdAt.toString().split(' ')[0];
              return '* $dateOnly | ${idea.content}';
            }).join('\n');

      // Log the formatted ideas text
      Logger.debug('Formatted ideas text for prompt:');
      Logger.debug(ideasText);

      // CRITICAL CHECK: Verify the ideas text is not empty
      if (ideasText == "No ideas found in this ideabook yet." && ideas.isNotEmpty) {
        Logger.debug('CRITICAL ERROR: Ideas text is empty even though we have ${ideas.length} ideas!');
      }

      // Get the past 25 chat messages for context
      final pastChatMessages = state.messages.length > 25
          ? state.messages.sublist(state.messages.length - 25)
          : state.messages;

      // Format the past chat messages
      final pastChatText = pastChatMessages.isEmpty
          ? ""
          : pastChatMessages.map((msg) =>
              "${msg.role == MessageRole.user ? 'User' : 'Assistant'}: ${msg.content}"
            ).join('\n');

      // Create the prompt for Gemma with the new format
      final promptContent = '''
You're an ideabook - a book collecting user's ideas. Your task is to understand user's intention and answer their prompts based on ideas captured so far.

Your name as an ideabook: $ideabookName
Ideas captured:
(format: idea creation date | idea content)
"""
$ideasText
"""

User's prompt:
"""
$content
"""

Previous chat history:
"""
$pastChatText
"""

There are several guidelines you MUST follow no matter what the user asks:
* Respond in a friendly, engaging, and conversational style like talking to a close friend
* Do not disclose details of yourself even if user asks (e.g. model name, etc). You are just an ideabook.
* The maximum length of your response is 5000 words no matter what the user asks.

Your response style:
* Prefer being brief and to the point unless it's more appropriate to be more detailed.
* Prefer responding based on the ideas captured.
* When responding with information outside the ideabook, friendly inform the user that and you may make mistake.
* Use emojis and emoticons in your response to make it more fun and engaging unless user asks not to.
* Respond in user's language of choice. If user doesn't specify, guess based on the user input language (e.g. if user input is a mix of English and Chinese, respond in Chinese or English or mix as appropriate).

Also summarize the current prompt and all of user's previous prompts into a short summary prompt in one sentence so that the summary prompt can be used as a new prompt for future conversation. Summarize from user's point of view, e.g. "I want to ..." instead of "The user wants to ...". Summarize in user's language (or mix of languages). IMPORTANT: The summary prompt should only include user's prompts, not ideas in the ideabook nor your response.

Output in JSON format:
{
  "user_prompt": "summary of user's prompts",
  "response": "your response to the user's prompt"
}
''';

      // Log the complete prompt for debugging
      Logger.debug('Complete prompt for Gemma:');
      Logger.debug(promptContent);
      Logger.debug('======= END CHAT DEBUGGING =======');

      final promptMessage = ChatMessage(
        id: IdUtils.generateId(),
        role: MessageRole.user,
        content: promptContent,
        timestamp: DateTime.now(),
      );

      // Send the message to Gemini
      final response = await _geminiChatService.chat([promptMessage]);

      if (response.isSuccess && response.content != null) {
        // The response content is already parsed by the Gemini service
        String responseContent = response.content!;

        // Variables to store parsed data
        String userPromptSummary = "";

        // Check if we have the original JSON from the Gemini service
        if (response.originalJson != null) {
          Logger.debug('Using originalJson from Gemini service');

          // Extract the user_prompt if it exists
          if (response.originalJson!.containsKey('user_prompt')) {
            userPromptSummary = response.originalJson!['user_prompt'] as String;
            Logger.debug('User prompt summary from originalJson: $userPromptSummary');
          } else {
            Logger.debug('No user_prompt field found in originalJson');
          }
        } else {
          Logger.debug('No originalJson available from Gemini service, trying to parse from content');

          // Check if the content might still be JSON or wrapped in markdown code blocks
          String contentToProcess = responseContent;

          // Check for markdown code blocks with ```json or ``` pattern
          if (contentToProcess.trim().startsWith('```')) {
            Logger.debug('Content appears to be wrapped in markdown code blocks, extracting...');

            // Find the end of the code block
            final endBlockIndex = contentToProcess.lastIndexOf('```');
            if (endBlockIndex > 3) {
              // Extract content between the code blocks
              final startContentIndex = contentToProcess.indexOf('\n', contentToProcess.indexOf('```')) + 1;
              if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
                contentToProcess = contentToProcess.substring(startContentIndex, endBlockIndex).trim();
                Logger.debug('Extracted content from markdown code block: ${contentToProcess.substring(0, contentToProcess.length.clamp(0, 100))}${contentToProcess.length > 100 ? "..." : ""}');
              }
            }
          }

          // Check if the content might be JSON
          if (contentToProcess.trim().startsWith('{') && contentToProcess.trim().endsWith('}')) {
            try {
              // Try to parse the JSON response
              final jsonResponse = json.decode(contentToProcess) as Map<String, dynamic>;

              // Extract the response content if it exists
              if (jsonResponse.containsKey('response')) {
                responseContent = jsonResponse['response'] as String;

                // Extract and store the user prompt summary if it exists
                if (jsonResponse.containsKey('user_prompt')) {
                  userPromptSummary = jsonResponse['user_prompt'] as String;
                  Logger.debug('User prompt summary from parsed content: $userPromptSummary');
                }

                // Log the parsed response
                Logger.debug('Successfully parsed JSON response in chat provider:');
                Logger.debug('Response content: $responseContent');
              }
            } catch (e) {
              // If parsing fails, use the content as is (already handled by Gemma service)
              Logger.debug('Content appears to be JSON but parsing failed, using as is: $e');
            }
          }
        }

        // Create metadata with user prompt if available
        Map<String, dynamic>? metadata;
        if (userPromptSummary.isNotEmpty) {
          metadata = {'user_prompt': userPromptSummary};
          Logger.debug('Created metadata with user_prompt: "$userPromptSummary"');
        } else {
          Logger.debug('No user_prompt summary available for metadata');
        }

        // Create a new assistant message
        final assistantMessage = ChatMessage(
          id: IdUtils.generateId(),
          role: MessageRole.assistant,
          content: responseContent,
          timestamp: DateTime.now(),
          metadata: metadata,
        );

        // Add the assistant message to the database
        await _repository.addMessageToChat(
          chat: chat!,
          role: MessageRole.assistant,
          content: responseContent,
          metadata: metadata,
        );

        // Update the state with the assistant message
        state = state.copyWith(
          messages: [...state.messages, assistantMessage],
          isLoading: false,
        );
      } else {
        // Handle error
        Logger.error('Error from Gemini API', response.errorMessage);
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Error from AI: ${response.errorMessage ?? "Something went wrong. Please try again later."}',
        );
      }
    } catch (e) {
      Logger.error('Error sending message', e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error sending message: $e',
      );
    }
  }

  /// Clear the error message
  void clearError() {
    state = state.clearError();
  }

  /// Update a message in the chat
  Future<bool> updateMessage(String messageId, {required bool isSavedAsNote}) async {
    try {
      // Find the message in the current state
      final messageIndex = state.messages.indexWhere((m) => m.id == messageId);
      if (messageIndex == -1) {
        Logger.error('Message not found in chat state: $messageId');
        return false;
      }

      // Get the original message
      final originalMessage = state.messages[messageIndex];

      // Create an updated message
      final updatedMessage = originalMessage.copyWith(
        isSavedAsNote: isSavedAsNote,
      );

      // Get the chat from the database
      final chat = await _repository.getChatByIdeabookId(_ideabookId);
      if (chat == null) {
        Logger.error('Chat not found for ideabook: $_ideabookId');
        return false;
      }

      // Update the message in the repository
      final success = await _repository.updateMessage(
        chat: chat,
        messageId: messageId,
        updatedMessage: updatedMessage,
      );

      if (success) {
        // Update the message in the state
        final updatedMessages = List<ChatMessage>.from(state.messages);
        updatedMessages[messageIndex] = updatedMessage;

        // Update the state
        state = state.copyWith(messages: updatedMessages);
        Logger.debug('Message $messageId updated successfully in state');
      }

      return success;
    } catch (e) {
      Logger.error('Error updating message', e);
      return false;
    }
  }

  /// Clear all chat history
  Future<void> clearChatHistory() async {
    try {
      Logger.debug('Clearing chat history for ideabook: $_ideabookId');

      // Get the current chat
      final chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat != null) {
        // Delete the existing chat from the database
        await _repository.deleteChat(chat.id);
        Logger.debug('Deleted chat with ID: ${chat.id}');

        // Create a new empty chat
        await _repository.createChat(ideabookId: _ideabookId);
        Logger.debug('Created new empty chat for ideabook: $_ideabookId');
      }

      // Clear the messages in the UI
      state = state.copyWith(messages: []);
      Logger.debug('Chat history cleared successfully');
    } catch (e) {
      Logger.error('Error clearing chat history', e);
      state = state.copyWith(errorMessage: 'Error clearing chat history: $e');
    }
  }

  /// Set an error message in the state
  void setError(String message) {
    state = state.copyWith(errorMessage: message);
  }
}

/// Provider for the chat notifier
final chatNotifierProvider = StateNotifierProvider.family<ChatNotifier, ChatState, String>((ref, ideabookId) {
  final repository = ref.watch(chatRepositoryProvider);
  final geminiChatService = ref.watch(geminiChatServiceProvider);
  return ChatNotifier(repository, geminiChatService, ideabookId);
});
