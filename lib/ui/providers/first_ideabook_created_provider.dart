import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/utils/logger.dart';

/// Provider that tracks when the first ideabook is created
/// This is used to trigger the first ideabook tour
final firstIdeabookCreatedProvider = StateProvider<bool>((ref) {
  Logger.debug('Initializing firstIdeabookCreatedProvider with false');

  // Add a listener to log when the state changes
  ref.listenSelf((previous, next) {
    Logger.debug('firstIdeabookCreatedProvider changed from $previous to $next');
  });

  return false;
});
