import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/repositories/repositories.dart';
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/utils/logger.dart';

/// Provider for all ideabooks from the database
final ideabooksProvider = FutureProvider<List<Ideabook>>((ref) async {
  final repository = ref.watch(ideabookRepositoryProvider);
  return repository.getAllIdeabooks();
});

/// Provider for a specific ideabook by ID
final ideabookProvider = FutureProvider.family<Ideabook?, String>((ref, id) async {
  final repository = ref.watch(ideabookRepositoryProvider);
  return repository.getIdeabookById(id);
});

/// Notifier for managing ideabooks
class IdeabooksNotifier extends StateNotifier<AsyncValue<List<Ideabook>>> {
  final IdeabookRepository _repository;

  IdeabooksNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadIdeabooks();
  }

  /// Load all ideabooks from the database
  Future<List<Ideabook>?> _loadIdeabooks() async {
    try {
      state = const AsyncValue.loading();
      final ideabooks = await _repository.getAllIdeabooks();
      Logger.debug('Loaded ${ideabooks.length} ideabooks from database');
      state = AsyncValue.data(ideabooks);
      return ideabooks;
    } catch (e, stack) {
      Logger.error('Error loading ideabooks', e);
      state = AsyncValue.error(e, stack);
      return null;
    }
  }

  /// Refresh the ideabooks list
  Future<void> refresh() async {
    await _loadIdeabooks();
  }

  /// Create a new ideabook
  Future<Ideabook> createIdeabook({
    required String name,
    IdeabookColor color = IdeabookColor.none,
    bool isLocked = false,
    Function? onCreated,
  }) async {
    final newIdeabook = await _repository.createIdeabook(
      name: name,
      color: color,
      isLocked: isLocked,
    );

    // Refresh the list
    await _loadIdeabooks();

    // Call the callback if provided
    if (onCreated != null) {
      onCreated();
    }

    return newIdeabook;
  }

  /// Update an ideabook
  Future<bool> updateIdeabook(Ideabook ideabook) async {
    final result = await _repository.updateIdeabook(ideabook);

    // Refresh the list
    if (result) {
      await _loadIdeabooks();
      Logger.debug('Reloaded ideabooks after updating ideabook ${ideabook.id}');
    }

    return result;
  }

  /// Delete an ideabook
  Future<bool> deleteIdeabook(String id) async {
    try {
      // Delete the ideabook regardless of lock state
      final result = await _repository.deleteIdeabook(id);

      // Refresh the list
      if (result) {
        await _loadIdeabooks();
        Logger.debug('Reloaded ideabooks after deleting ideabook $id');
      }

      return result;
    } catch (e) {
      Logger.error('Error deleting ideabook', e);
      rethrow; // Rethrow to handle in the UI
    }
  }

  /// Update an ideabook's color
  Future<bool> updateIdeabookColor(String ideabookId, IdeabookColor newColor) async {
    // Get the current state
    if (state is! AsyncData<List<Ideabook>>) {
      return false;
    }

    final ideabooks = (state as AsyncData<List<Ideabook>>).value;
    final ideabook = ideabooks.firstWhere(
      (ideabook) => ideabook.id == ideabookId,
      orElse: () => throw Exception('Ideabook not found'),
    );

    // Update the ideabook
    final updatedIdeabook = ideabook.copyWith(
      color: newColor,
      updatedAt: DateTime.now(),
    );

    return updateIdeabook(updatedIdeabook);
  }

  /// Toggle an ideabook's lock state
  Future<bool> toggleIdeabookLock(String ideabookId) async {
    Logger.debug('IdeabooksNotifier: Toggling lock state for ideabook: $ideabookId');
    Logger.debug('IdeabooksNotifier: Current state type: ${state.runtimeType}');

    // If state is loading, wait for it to complete
    if (state is AsyncLoading) {
      Logger.debug('IdeabooksNotifier: State is AsyncLoading, waiting for data to load...');
      try {
        // Wait for ideabooks to load (with timeout)
        final ideabooks = await _loadIdeabooks();
        if (ideabooks == null) {
          Logger.error('IdeabooksNotifier: Failed to load ideabooks');
          return false;
        }
        Logger.debug('IdeabooksNotifier: Ideabooks loaded successfully, proceeding with toggle');
      } catch (e) {
        Logger.error('IdeabooksNotifier: Error waiting for ideabooks to load', e);
        return false;
      }
    }

    // Check state again after potential loading
    if (state is! AsyncData<List<Ideabook>>) {
      Logger.error('IdeabooksNotifier: Cannot toggle lock state - state is not AsyncData after waiting');

      // Try to force a refresh and return false
      _loadIdeabooks();
      return false;
    }

    // Now we should have AsyncData
    final ideabooks = (state as AsyncData<List<Ideabook>>).value;

    // Find the ideabook
    Ideabook? ideabook;
    try {
      ideabook = ideabooks.firstWhere(
        (book) => book.id == ideabookId,
        orElse: () => throw Exception('Ideabook not found'),
      );
    } catch (e) {
      Logger.error('IdeabooksNotifier: Error finding ideabook', e);
      return false;
    }

    Logger.debug('IdeabooksNotifier: Found ideabook: ${ideabook.id}, current lock state: ${ideabook.isLocked}');

    // Update the ideabook with toggled lock state
    final updatedIdeabook = ideabook.copyWith(
      isLocked: !ideabook.isLocked,
      updatedAt: DateTime.now(),
    );

    Logger.debug('IdeabooksNotifier: Created updated ideabook with new lock state: ${updatedIdeabook.isLocked}');

    final result = await updateIdeabook(updatedIdeabook);
    Logger.debug('IdeabooksNotifier: Update result: $result');

    return result;
  }
}

/// Provider for the ideabooks notifier
final ideabooksNotifierProvider = StateNotifierProvider<IdeabooksNotifier, AsyncValue<List<Ideabook>>>((ref) {
  final repository = ref.watch(ideabookRepositoryProvider);
  return IdeabooksNotifier(repository);
});

/// Provider to track when a new ideabook is created
/// This will be used to trigger scrolling to the bottom of the list
final newIdeabookCreatedProvider = StateProvider<bool>((ref) => false);
