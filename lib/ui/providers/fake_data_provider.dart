import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';

/// A fake notifier for ideabooks used in tests
class FakeIdeabooksNotifier extends StateNotifier<List<Ideabook>> {
  FakeIdeabooksNotifier() : super([]);

  /// Update an ideabook's color
  void updateIdeabookColor(String ideabookId, IdeabookColor newColor) {
    state = state.map((ideabook) {
      if (ideabook.id == ideabookId) {
        return ideabook.copyWith(color: newColor);
      }
      return ideabook;
    }).toList();
  }
}

/// A fake provider for ideabooks used in tests
final fakeIdeabooksProvider = StateNotifierProvider<FakeIdeabooksNotifier, List<Ideabook>>((ref) {
  return FakeIdeabooksNotifier();
});
