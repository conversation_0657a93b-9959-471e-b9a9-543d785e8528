import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/auth/auth_providers.dart';

/// Simple model to represent a user
class User {
  final String id;
  final String displayName;
  final String email;
  final String? photoUrl;

  User({
    required this.id,
    required this.displayName,
    required this.email,
    this.photoUrl,
  });

  /// Create a User from a Firebase User
  factory User.fromFirebaseUser(firebase_auth.User firebaseUser) {
    return User(
      id: firebaseUser.uid,
      displayName: firebaseUser.displayName ?? 'User',
      email: firebaseUser.email ?? '',
      photoUrl: firebaseUser.photoURL,
    );
  }
}

/// Provider for the current user
final currentUserProvider = Provider<User?>((ref) {
  final firebaseUser = ref.watch(firebaseUserProvider).value;
  if (firebaseUser == null) {
    return null;
  }
  return User.fromFirebaseUser(firebaseUser);
});
