import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/firestore_listener_provider.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';

/// Provider for ideabooks using Firestore with optimized listener pool
final combinedIdeabooksNotifierProvider = Provider<StateNotifierProvider<StateNotifier<AsyncValue<List<Ideabook>>>, AsyncValue<List<Ideabook>>>>((ref) {
  Logger.debug('Using Firestore with optimized listener pool for ideabooks');
  return firestoreIdeabooksListenerNotifierProvider;
});

/// Provider to check if Firestore is being used for ideabooks
/// Always returns true since we've migrated fully to Firestore
final isUsingFirestoreProvider = Provider<bool>((ref) {
  return true;
});

/// Provider to control Firestore listeners
/// This provider is used to start and stop Firestore listeners
/// when navigating to and from the ideabooks list screen
class FirestoreListenerController extends StateNotifier<bool> {
  final Ref _ref;

  FirestoreListenerController(this._ref) : super(false);

  /// Start listening to Firestore updates
  void startListening() {
    if (state) {
      Logger.debug('Firestore listener already active');
      return;
    }

    Logger.debug('Starting Firestore listener with optimized listener pool');

    try {
      // Start listening
      final notifier = _ref.read(firestoreIdeabooksListenerNotifierProvider.notifier);
      notifier.startListening();

      // Listen for errors in the ideabooks provider
      _ref.listen(firestoreIdeabooksListenerNotifierProvider, (previous, current) {
        if (current is AsyncError) {
          // Check if this is a permission error
          final isPermissionError = current.error != null && ErrorUtils.isPermissionError(current.error!);

          if (isPermissionError) {
            Logger.error('Permission error detected in ideabooks listener controller', current.error);
            // Force stop listening to avoid continuous permission errors
            try {
              // Call directly to the notifier to stop listening
              final notifier = _ref.read(firestoreIdeabooksListenerNotifierProvider.notifier);
              notifier.stopListening();

              // Update our state
              state = false;

              Logger.debug('Successfully stopped ideabooks listener due to permission error');

              // Handle the permission error globally
              ErrorUtils.handleGlobalPermissionError(_ref, current.error!);
            } catch (e) {
              Logger.error('Error stopping ideabooks listener after permission error', e);
            }
          }
        }
      });

      state = true;
    } catch (e) {
      Logger.error('Error starting Firestore ideabooks listener', e);

      // Check if this is a permission error
      if (ErrorUtils.isPermissionError(e)) {
        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      }

      state = false;
    }
  }

  /// Stop listening to Firestore updates
  void stopListening() {
    if (!state) {
      Logger.debug('Firestore listener not active');
      return;
    }

    Logger.debug('Stopping Firestore listener');
    final notifier = _ref.read(firestoreIdeabooksListenerNotifierProvider.notifier);
    notifier.stopListening();
    state = false;
  }
}

/// Provider for the Firestore listener controller
final firestoreListenerControllerProvider = StateNotifierProvider<FirestoreListenerController, bool>((ref) {
  return FirestoreListenerController(ref);
});
