import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:voji/services/auth/auth_providers.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';

/// Sign in screen for the app
class SignInScreen extends ConsumerWidget {
  /// Constructor
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: null,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Title
            Text(
              'Sign in to Voji',
              style: GoogleFonts.afacad(
                fontSize: 24,
                fontWeight: FontWeight.w500,
                color: VojiTheme.colorsOf(context).textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // Google sign-in button
            _buildSignInButton(
              context,
              'Sign in with Google',
              Icons.login,
              onPressed: () async {
                try {
                  // Show loading indicator
                  _showLoadingDialog(context);

                  Logger.debug('Sign in button pressed - starting Google sign-in flow');

                  // Get the auth service
                  final authService = ref.read(authServiceProvider);

                  // Sign in with Google
                  final userCredential = await authService.signInWithGoogle();

                  // Close loading dialog
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }

                  // If sign-in was successful, go back to previous screen
                  if (userCredential != null && context.mounted) {
                    Logger.debug('Sign-in successful, returning to previous screen');

                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Signed in as ${userCredential.user?.displayName}',
                          style: GoogleFonts.afacad(),
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );

                    Navigator.of(context).pop();
                  } else if (context.mounted) {
                    // User canceled the sign-in
                    Logger.debug('Sign-in canceled by user');
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Sign-in canceled',
                          style: GoogleFonts.afacad(),
                        ),
                      ),
                    );
                  }
                } catch (e) {
                  // Close loading dialog
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }

                  // Show error message
                  Logger.error('Error signing in with Google', e);

                  // Format error message for display
                  String errorMessage = 'Error signing in';
                  if (e.toString().contains('network')) {
                    errorMessage = 'Network error. Check your connection.';
                  } else if (e.toString().contains('canceled')) {
                    errorMessage = 'Sign-in canceled.';
                  } else if (ErrorUtils.isPermissionError(e)) {
                    // Use generic message for permission errors including App Check failures
                    errorMessage = ErrorUtils.permissionDeniedMessage;
                  } else {
                    // For other errors, show a generic message instead of the full error
                    errorMessage = 'Error signing in. Please try again later.';
                  }

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          errorMessage,
                          style: GoogleFonts.afacad(),
                        ),
                        backgroundColor: Colors.red[300],
                      ),
                    );
                  }
                }
              },
            ),

            const SizedBox(height: 16),

            // Placeholder for future sign-in options
            // This is commented out for now but can be uncommented when needed
            /*
            _buildSignInButton(
              context,
              'Sign in with Apple',
              Icons.apple,
              onPressed: () {
                // This will be implemented later
              },
            ),

            const SizedBox(height: 16),

            _buildSignInButton(
              context,
              'Sign in with Email',
              Icons.email,
              onPressed: () {
                // This will be implemented later
              },
            ),
            */
          ],
        ),
      ),
    );
  }

  /// Show a loading dialog
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Theme.of(context).cardColor,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(
                'Signing in...',
                style: VojiTheme.textStylesOf(context).bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a sign-in button with the given text and icon
  Widget _buildSignInButton(
    BuildContext context,
    String text,
    IconData icon, {
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border.all(
          color: VojiTheme.colorsOf(context).border,
          width: 2,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: VojiTheme.colorsOf(context).textPrimary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  text,
                  style: GoogleFonts.afacad(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: VojiTheme.colorsOf(context).textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
