import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:voji/models/ideabook.dart';
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/services/back_button_service.dart';
import 'package:voji/ui/providers/idea_edit_provider.dart';
import 'package:voji/ui/providers/ideabook_detail_tab_provider.dart';
import 'package:voji/ui/providers/ideabook_provider.dart' show ideabookProvider, ideabooksNotifierProvider;
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/chat_tab.dart';
import 'package:voji/ui/widgets/ideabook_detail_bottom_panel_controller.dart';
import 'package:voji/ui/widgets/ideas_tab.dart';
import 'package:voji/ui/widgets/notes_tab.dart';
import 'package:voji/utils/logger.dart';

/// Screen showing the details of an ideabook
class IdeabookDetailScreen extends ConsumerStatefulWidget {
  /// The ID of the ideabook to display
  final String ideabookId;

  /// The ideabook object (optional)
  /// If provided, it will be used instead of fetching from Firestore
  final Ideabook? ideabook;

  /// Constructor
  const IdeabookDetailScreen({
    super.key,
    required this.ideabookId,
    this.ideabook,
  });

  @override
  ConsumerState<IdeabookDetailScreen> createState() => _IdeabookDetailScreenState();
}

class _IdeabookDetailScreenState extends ConsumerState<IdeabookDetailScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late TabController _tabController;
  int _currentIndex = 0;

  // State for ideabook name editing
  bool _isEditingName = false;
  final TextEditingController _nameController = TextEditingController();
  final FocusNode _nameFocusNode = FocusNode();

  // Keep track of the current ideabook to avoid unnecessary Firestore updates
  Ideabook? _currentIdeabook;

  // Local state for the ideabook name to persist across tab changes
  String? _localIdeabookName;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabSelection);

    // Register as an observer to detect app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Add focus listener to detect clicks outside the text field
    _nameFocusNode.addListener(_handleNameFocusChange);

    // Add a post-frame callback to refresh the ideabook data when the screen is first shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshIdeabookData();

      // Set up back button handler
      _updateBackButtonHandler();
    });
  }

  /// Update the back button handler based on the current tab
  void _updateBackButtonHandler() {
    BackButtonService.instance.setBackButtonHandler(
      isInChatOrNotesTab: _currentIndex != 0,
      onBackPressed: _currentIndex != 0 ? _handleBackButton : null,
    );
  }

  /// Handle back button press
  void _handleBackButton() {
    // If we're not on the Ideas tab, navigate to it first
    if (_currentIndex != 0) {
      _tabController.animateTo(0);
    }
  }

  @override
  void didUpdateWidget(IdeabookDetailScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the ideabook ID changed, refresh the data and reset local state
    if (oldWidget.ideabookId != widget.ideabookId) {
      // Reset local state
      setState(() {
        _localIdeabookName = null;
      });

      // Refresh data from Firestore
      _refreshIdeabookData();
    }
  }

  /// Handle focus changes for the name text field
  void _handleNameFocusChange() {
    // If focus is lost, exit edit mode and save changes if needed
    if (!_nameFocusNode.hasFocus && _isEditingName) {
      _saveNameChanges();
    }
  }

  /// Save name changes if the name has changed
  void _saveNameChanges() {
    if (_currentIdeabook == null) return;

    // Get the trimmed text
    final newName = _nameController.text.trim();

    // Check if name is too long (maximum 100 characters)
    if (newName.length > maxIdeabookNameLength) {
      // Show error notification
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Ideabook name must be $maxIdeabookNameLength characters or less'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.red,
        ),
      );
      // Don't exit edit mode or save changes
      setState(() {
        _isEditingName = true; // Ensure we stay in edit mode
      });
      return;
    }

    // Check if the name has actually changed from the current local name
    // This ensures we don't send a write request if the user just presses Enter without changing anything
    final hasNameChanged = newName.isNotEmpty && newName != _localIdeabookName;

    // Only save if the name has changed and is not empty
    if (hasNameChanged) {
      // Update local state immediately to provide faster feedback
      setState(() {
        _localIdeabookName = newName;
      });

      // Create updated ideabook with the new name
      final updatedIdeabook = _currentIdeabook!.copyWith(
        name: newName,
        updatedAt: DateTime.now(),
      );

      // Update the ideabook in Firestore
      Logger.debug('Updating ideabook name in Firestore: ${updatedIdeabook.id}, new name: $newName');
      ref.read(ideabooksNotifierProvider.notifier).updateIdeabook(updatedIdeabook).then(
        (success) {
          // Only show notification if the update was successful and the widget is still mounted
          if (success && mounted) {
            // Show notification as a SnackBar
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Ideabook name updated'),
                duration: Duration(seconds: 1),
              ),
            );
          }
        },
      );

      // Refresh the ideabook data as needed
      _refreshIdeabookData();
    }

    // Exit edit mode regardless of whether we saved changes
    setState(() {
      _isEditingName = false;
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    _nameController.dispose();
    _nameFocusNode.removeListener(_handleNameFocusChange);
    _nameFocusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);

    // Reset back button handler
    BackButtonService.instance.setBackButtonHandler(
      isInChatOrNotesTab: false,
      onBackPressed: null,
    );

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh the ideabook data when the app is resumed
    if (state == AppLifecycleState.resumed) {
      _refreshIdeabookData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This will be called when the widget is inserted into the tree
    // and when the dependencies change (including when the app is resumed)
    _refreshIdeabookData();
  }

  /// Refresh the ideabook data from the database
  void _refreshIdeabookData() {
    Logger.debug('Refreshing ideabook data for ID: ${widget.ideabookId}');
    ref.invalidate(ideabookProvider(widget.ideabookId));
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging || _currentIndex != _tabController.index) {
      setState(() {
        _currentIndex = _tabController.index;
      });
      // Update the tab provider
      ref.read(ideabookDetailTabProvider.notifier).state = _currentIndex;

      // Update back button handler
      _updateBackButtonHandler();
    }
  }

  /// Build loading indicator
  Widget _buildLoadingIndicator(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// Build error indicator
  Widget _buildErrorIndicator(BuildContext context, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: VojiTheme.colorsOf(context).error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading ideabook',
            style: VojiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: VojiTheme.textStylesOf(context).bodySmall,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Refresh the ideabook
              final _ = ref.refresh(ideabookProvider(widget.ideabookId));
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // No longer listen to ideabook document changes to minimize Firestore reads
    // The ideabook details will only be refreshed when explicitly needed

    // If an ideabook was passed directly, use it instead of fetching from Firestore
    if (widget.ideabook != null) {
      // Use the passed ideabook directly
      return _buildContent(context, widget.ideabook!);
    }

    // Otherwise, get the ideabook from the provider
    final ideabookAsync = ref.watch(ideabookProvider(widget.ideabookId));

    return ideabookAsync.when(
      loading: () => Scaffold(
        body: _buildLoadingIndicator(context),
      ),
      error: (error, _) => Scaffold(
        body: _buildErrorIndicator(context, error),
      ),
      data: (ideabook) {
        if (ideabook == null) {
          return Scaffold(
            body: Center(
              child: Text(
                'Ideabook not found',
                style: VojiTheme.textStylesOf(context).bodyLarge,
              ),
            ),
          );
        }

        return _buildContent(context, ideabook);
      },
    );
  }

  /// Build the main content of the screen with the ideabook data
  Widget _buildContent(BuildContext context, Ideabook ideabook) {
    // Initialize local name state if it's null using null-aware assignment
    _localIdeabookName ??= ideabook.name;

    // Get the ideabook color and appropriate text color
    final ideabookColor = VojiTheme.getIdeabookColor(context, ideabook.color.index);
    final textColor = VojiTheme.getTextColorForIdeabookColor(context, ideabook.color.index);

    // We'll handle back navigation through the back button in the app bar
    // Wrap the Scaffold in a GestureDetector to intercept taps when in edit mode
    return GestureDetector(
      // When in edit mode, intercept taps outside the text field to save changes
      onTap: _isEditingName ? () {
        // Save changes when tapping outside the text field
        _saveNameChanges();
      } : null,
      child: Scaffold(
      appBar: AppBar(
        // Custom title widget with explicit layout
        title: null, // Remove default title
        automaticallyImplyLeading: false, // Don't automatically add back button
        backgroundColor: ideabookColor,
        flexibleSpace: SafeArea(
          child: Row(
            children: [
              // Back button (left-aligned)
              IconButton(
                icon: Icon(Icons.arrow_back, color: textColor),
                onPressed: () {
                  // If we're not on the Ideas tab, navigate to it first
                  if (_currentIndex != 0) {
                    _tabController.animateTo(0);
                  } else {
                    // Otherwise, pop the screen
                    Navigator.of(context).pop();
                  }
                },
              ),

              // Title (right-aligned)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0), // Add right padding
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: _isEditingName
                        ? Transform.translate(
                            // Use Transform.translate to move the widget up without negative margins
                            offset: const Offset(0, -4),
                            child: Container(
                              // Use the same height constraints as the text to maintain alignment
                              height: 40, // Fixed height to match text display mode
                              alignment: Alignment.centerRight,
                              child: TextField(
                                controller: _nameController,
                                focusNode: _nameFocusNode,
                                style: GoogleFonts.afacad(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: textColor,
                                ),
                                textAlign: TextAlign.right,
                                textAlignVertical: TextAlignVertical.center, // Center text vertically
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 8.0), // Reduced vertical padding
                                  isDense: true,
                                  isCollapsed: false, // Don't collapse to ensure proper alignment
                                ),
                                maxLines: 1, // Restrict to single line
                                keyboardType: TextInputType.text, // Use text keyboard
                                textInputAction: TextInputAction.done, // Show "done" action on keyboard
                                onSubmitted: (_) => _saveNameChanges(), // Save when Enter/Done is pressed
                              ),
                            ),
                          )
                        : GestureDetector(
                            onTap: () {
                              // Enter edit mode when tapping on the title
                              setState(() {
                                _isEditingName = true;
                                _currentIdeabook = ideabook;
                                // Use the local name state for editing, which might be different from ideabook.name
                                _nameController.text = _localIdeabookName!;
                              });

                              // Request focus after the state update is complete
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _nameFocusNode.requestFocus();
                              });
                            },
                            child: Container(
                              // Use a container with the same height to ensure consistent positioning
                              height: 40, // Fixed height for both modes
                              alignment: Alignment.centerRight,
                              child: Text(
                                _localIdeabookName!, // Use local state variable instead of ideabook.name
                                style: GoogleFonts.afacad(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // Main content
          Expanded(
            child: Builder(
              builder: (context) {
                // Create a modified ideabook with our local name
                // This ensures the tabs have the most up-to-date name
                final updatedIdeabook = ideabook.copyWith(name: _localIdeabookName!);

                return TabBarView(
                  controller: _tabController,
                  // Disable swipe gestures to switch tabs
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    // Ideas tab
                    IdeasTab(ideabook: updatedIdeabook),

                    // Chat tab
                    ChatTab(ideabook: updatedIdeabook),

                    // Notes tab
                    NotesTab(ideabook: updatedIdeabook),
                  ],
                );
              }
            ),
          ),

          // Bottom panel controller - only show in Ideas tab and when not in edit mode
          if (_currentIndex == 0 && ref.watch(ideaEditProvider) == null) // 0 is the index of the Ideas tab
            IdeabookDetailBottomPanelController(
              ideabookId: widget.ideabookId,
            ),
        ],
      ),
      // Use a completely custom bottom navigation approach instead of TabBar
      bottomNavigationBar: Material(
        color: ideabookColor,
        child: SafeArea(
          top: false,
          child: SizedBox(
            height: 60, // Taller height
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Ideas tab
                Expanded(
                  child: InkWell(
                    onTap: () {
                      _tabController.animateTo(0);
                    },
                    child: Container(
                      height: 60,
                      color: ideabookColor,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _currentIndex == 0 ? Icons.lightbulb : Icons.lightbulb_outline,
                            color: textColor,
                            size: 24,
                          ),
                          SizedBox(width: 6),
                          Text(
                            'Ideas',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Chat tab
                Expanded(
                  child: InkWell(
                    onTap: () {
                      // Animate to chat tab
                      _tabController.animateTo(1);
                    },
                    child: Container(
                      height: 60,
                      color: ideabookColor,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _currentIndex == 1 ? Icons.chat_bubble : Icons.chat_bubble_outline,
                            color: textColor,
                            size: 24,
                          ),
                          SizedBox(width: 6),
                          Text(
                            'Chat',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Notes tab
                Expanded(
                  child: InkWell(
                    onTap: () {
                      _tabController.animateTo(2);
                    },
                    child: Container(
                      height: 60,
                      color: ideabookColor,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _currentIndex == 2 ? Icons.note_alt : Icons.note_alt_outlined,
                            color: textColor,
                            size: 24,
                          ),
                          SizedBox(width: 6),
                          Text(
                            'Notes',
                            style: GoogleFonts.afacad(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    );
  }
}
