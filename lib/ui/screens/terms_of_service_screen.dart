import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/ui/theme/theme_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/logger.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// Screen to display the Terms of Service
class TermsOfServiceScreen extends ConsumerStatefulWidget {
  /// Constructor
  const TermsOfServiceScreen({super.key});

  @override
  ConsumerState<TermsOfServiceScreen> createState() => _TermsOfServiceScreenState();
}

class _TermsOfServiceScreenState extends ConsumerState<TermsOfServiceScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _hasError = false;

  bool _isDarkMode = false; // Default to light theme

  @override
  void initState() {
    super.initState();

    // Initialize WebView in the next frame when context is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Get the current theme
        _isDarkMode = Theme.of(context).brightness == Brightness.dark;

        // Initialize the WebView with the current theme
        _initWebView();
      }
    });
  }

  Future<void> _initWebView() async {
    try {
      // Load the HTML content from the asset
      String htmlContent = await rootBundle.loadString('terms_of_service.html');

      // Inject a style tag to force the appropriate theme
      final themeStyle = _isDarkMode
          ? '<style>body { color: #ffffff; background-color: #121212; }</style>'
          : '<style>body { color: #000000; background-color: #ffffff; }</style>';

      // Insert the theme style right before the closing head tag
      htmlContent = htmlContent.replaceFirst('</head>', '$themeStyle</head>');

      // Create a WebViewController
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.transparent)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageFinished: (String url) {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
              }
            },
            onWebResourceError: (WebResourceError error) {
              Logger.error('Error loading Terms of Service', error.description);
              if (mounted) {
                setState(() {
                  _isLoading = false;
                  _hasError = true;
                });
              }
            },
          ),
        )
        ..loadHtmlString(htmlContent);

      Logger.debug('Terms of Service loaded with ${_isDarkMode ? 'dark' : 'light'} theme');
    } catch (e) {
      Logger.error('Error initializing Terms of Service WebView', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if theme has changed
    final newIsDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (_isDarkMode != newIsDarkMode && mounted) {
      _isDarkMode = newIsDarkMode;

      // If we haven't initialized the WebView yet, don't do anything
      // It will be initialized with the correct theme in initState
      if (_controller != null) {
        // Reload the WebView with the new theme
        setState(() {
          _initWebView();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Terms of Service',
          style: VojiTheme.textStylesOf(context).titleMedium,
        ),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(
        children: [
          if (!_hasError && _controller != null) WebViewWidget(controller: _controller!),
          if (_hasError || _controller == null)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _controller == null ? Icons.hourglass_empty : Icons.error_outline,
                      size: 48,
                      color: _controller == null ? Colors.blue : Colors.red
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _controller == null ? 'Loading Terms of Service...' : 'Unable to load Terms of Service',
                      style: VojiTheme.textStylesOf(context).bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _controller == null ? 'Please wait...' : 'Please try again later or contact support.',
                      style: VojiTheme.textStylesOf(context).bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          if (_isLoading && !_hasError && _controller != null)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
