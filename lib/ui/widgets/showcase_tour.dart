import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:voji/services/tour/tour_service.dart';
import 'package:voji/utils/logger.dart';

/// Global keys for showcase items
class ShowcaseKeys {
  /// Key for the new ideabook button
  static final newIdeabookButton = GlobalKey();

  /// Key for the first ideabook microphone button
  static final firstIdeabookMicButton = GlobalKey();

  /// Key for the ideas reordering tour
  static final ideasReorderingTour = GlobalKey();

  /// Key for the save as note button in chat
  static final saveAsNoteButton = GlobalKey();

  /// Key for the note detail refresh button
  static final noteDetailRefreshButton = GlobalKey();

  /// Key for the ideabook color system tour
  static final ideabookColorSystemTour = GlobalKey();
}

/// Widget that wraps the app with showcase functionality
class ShowcaseTour extends ConsumerStatefulWidget {
  /// The child widget to wrap
  final Widget child;

  /// Whether to start the showcase automatically
  final bool autoStart;

  /// Constructor
  const ShowcaseTour({
    super.key,
    required this.child,
    this.autoStart = true,
  });

  @override
  ConsumerState<ShowcaseTour> createState() => _ShowcaseTourState();
}

class _ShowcaseTourState extends ConsumerState<ShowcaseTour> {
  /// Whether the showcase has been initialized
  bool _initialized = false;

  @override
  void initState() {
    super.initState();

    // Initialize the showcase after the first frame
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeShowcase();
      });
    }
  }

  /// Initialize the showcase if it should be shown
  Future<void> _initializeShowcase() async {
    if (_initialized) {
      Logger.debug('Showcase tour already initialized, skipping');
      return;
    }

    try {
      // Use the TourService to show the ideabooks list tour
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (mounted) {
          final shown = await TourService.showIdeabooksListTour(context);
          Logger.debug('Ideabooks list tour shown: $shown');
        }
      });

      _initialized = true;
      Logger.debug('Showcase tour initialization completed');
    } catch (e) {
      Logger.error('Error initializing showcase', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Helper function to create a showcase widget
/// This is a wrapper around TourService.createShowcaseWidget
Widget createShowcase({
  required Widget child,
  required GlobalKey key,
  required String description,
  required BuildContext context,
  String? title,
  Color? overlayColor,
  Color? tooltipBackgroundColor,
  Color? textColor,
  EdgeInsets? tooltipPadding,
  bool showArrow = true,
  Duration movingAnimationDuration = const Duration(milliseconds: 700),
}) {
  return TourService.createShowcaseWidget(
    context: context,
    child: child,
    key: key,
    description: description,
    title: title,
    overlayColor: overlayColor,
    tooltipBackgroundColor: tooltipBackgroundColor,
    textColor: textColor,
    tooltipPadding: tooltipPadding,
    showArrow: showArrow,
    movingAnimationDuration: movingAnimationDuration,
  );
}
