import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/ui/providers/audio_providers.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/audio_recorder_controller.dart';
import 'package:voji/ui/widgets/audio_recording_waveform.dart';
import 'package:voji/utils/logger.dart';

/// Layout options for the advanced audio recording panel
enum AudioRecordingPanelLayout {
  /// Horizontal layout with buttons on the sides
  horizontal,

  /// Vertical layout with buttons at the bottom
  vertical,

  /// Compact layout with minimal UI
  compact,
}

/// A more advanced widget that displays an audio recording panel with
/// waveform visualization, timer, and control buttons
class AdvancedAudioRecordingPanel extends ConsumerStatefulWidget {
  /// Callback when recording is completed
  final Function(String filePath, Duration duration)? onRecordingCompleted;

  /// Callback when recording is cancelled
  final VoidCallback? onRecordingCancelled;

  /// Callback when recording fails
  final Function(String errorMessage)? onRecordingFailed;

  /// Callback when permission is denied
  final VoidCallback? onPermissionDenied;

  /// The layout of the panel
  final AudioRecordingPanelLayout layout;

  /// Whether to show the timer
  final bool showTimer;

  /// The color of the waveform and buttons
  final Color? color;

  /// The height of the waveform
  final double waveformHeight;

  /// Whether to automatically start recording when the widget is initialized
  final bool autoStart;

  /// Constructor
  const AdvancedAudioRecordingPanel({
    super.key,
    this.onRecordingCompleted,
    this.onRecordingCancelled,
    this.onRecordingFailed,
    this.onPermissionDenied,
    this.layout = AudioRecordingPanelLayout.horizontal,
    this.showTimer = false,
    this.color,
    this.waveformHeight = 40,
    this.autoStart = true,
  });

  @override
  ConsumerState<AdvancedAudioRecordingPanel> createState() => _AdvancedAudioRecordingPanelState();
}

class _AdvancedAudioRecordingPanelState extends ConsumerState<AdvancedAudioRecordingPanel> {
  // Local processing state for this recording instance
  AudioProcessingState _localProcessingState = AudioProcessingState.idle;

  @override
  void initState() {
    super.initState();

    // Reset the global state to avoid interference from other recording instances
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        ref.read(audioProcessingStateProvider.notifier).state = AudioProcessingState.idle;
      } catch (e) {
        Logger.debug('AdvancedAudioRecordingPanel: Could not reset global processing state: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AudioRecorderController(
      autoStart: widget.autoStart,
      onRecordingCompleted: (filePath, duration) {
        Logger.debug('AdvancedAudioRecordingPanel: recording completed, duration: ${duration.inSeconds}s');

        // Set the local state to processing
        setState(() {
          _localProcessingState = AudioProcessingState.processing;
        });

        if (widget.onRecordingCompleted != null) {
          widget.onRecordingCompleted!(filePath, duration);
        }
      },
      onRecordingCancelled: widget.onRecordingCancelled,
      onRecordingFailed: widget.onRecordingFailed,
      onPermissionDenied: widget.onPermissionDenied,
      builder: (
        context,
        recordingState,
        amplitude,
        duration,
        onCancel,
        onComplete,
        {bool isInCountdown = false, int? countdownSeconds}
      ) {
        final themeColor = widget.color ?? VojiTheme.colorsOf(context).textPrimary;

        // Build the appropriate layout
        switch (widget.layout) {
          case AudioRecordingPanelLayout.horizontal:
            return _buildHorizontalLayout(
              context,
              amplitude,
              duration,
              themeColor,
              onCancel,
              onComplete,
              _localProcessingState,
              isInCountdown: isInCountdown,
              countdownSeconds: countdownSeconds,
            );

          case AudioRecordingPanelLayout.vertical:
            return _buildVerticalLayout(
              context,
              amplitude,
              duration,
              themeColor,
              onCancel,
              onComplete,
              _localProcessingState,
              isInCountdown: isInCountdown,
              countdownSeconds: countdownSeconds,
            );

          case AudioRecordingPanelLayout.compact:
            return _buildCompactLayout(
              context,
              amplitude,
              duration,
              themeColor,
              onCancel,
              onComplete,
              _localProcessingState,
              isInCountdown: isInCountdown,
              countdownSeconds: countdownSeconds,
            );
        }
      },
    );
  }

  /// Build the horizontal layout
  Widget _buildHorizontalLayout(
    BuildContext context,
    double amplitude,
    Duration duration,
    Color color,
    VoidCallback onCancel,
    VoidCallback onComplete,
    AudioProcessingState processingState, {
    bool isInCountdown = false,
    int? countdownSeconds,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          // Cancel button
          IconButton(
            icon: Icon(
              Icons.close,
              color: color,
            ),
            onPressed: onCancel,
          ),

          // Waveform visualization with timer or countdown warning
          Expanded(
            child: AudioRecordingWaveform(
              amplitude: amplitude,
              duration: duration,
              showTimer: widget.showTimer,
              color: color,
              height: widget.waveformHeight,
              showCountdownWarning: isInCountdown,
              countdownSeconds: countdownSeconds,
            ),
          ),

          // Complete button or spinner
          processingState == AudioProcessingState.processing
              ? SizedBox(
                  width: 48,
                  height: 48,
                  child: Center(
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: color,
                      ),
                    ),
                  ),
                )
              : IconButton(
                  icon: Icon(
                    Icons.check,
                    color: color,
                  ),
                  onPressed: onComplete,
                ),
        ],
      ),
    );
  }

  /// Build the vertical layout
  Widget _buildVerticalLayout(
    BuildContext context,
    double amplitude,
    Duration duration,
    Color color,
    VoidCallback onCancel,
    VoidCallback onComplete,
    AudioProcessingState processingState, {
    bool isInCountdown = false,
    int? countdownSeconds,
  }) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Waveform visualization with timer or countdown warning
          SizedBox(
            height: widget.waveformHeight,
            child: AudioRecordingWaveform(
              amplitude: amplitude,
              duration: duration,
              showTimer: widget.showTimer,
              color: color,
              height: widget.waveformHeight,
              showCountdownWarning: isInCountdown,
              countdownSeconds: countdownSeconds,
            ),
          ),

          const SizedBox(height: 16),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Cancel button
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: color,
                ),
                onPressed: onCancel,
              ),

              const SizedBox(width: 32),

              // Complete button or spinner
              processingState == AudioProcessingState.processing
                  ? SizedBox(
                      width: 48,
                      height: 48,
                      child: Center(
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: color,
                          ),
                        ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.check,
                        color: color,
                      ),
                      onPressed: onComplete,
                    ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the compact layout
  Widget _buildCompactLayout(
    BuildContext context,
    double amplitude,
    Duration duration,
    Color color,
    VoidCallback onCancel,
    VoidCallback onComplete,
    AudioProcessingState processingState, {
    bool isInCountdown = false,
    int? countdownSeconds,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        children: [
          // Small cancel button
          IconButton(
            icon: Icon(
              Icons.close,
              color: color,
              size: 20,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            onPressed: onCancel,
          ),

          const SizedBox(width: 8),

          // Waveform visualization with timer or countdown warning
          Expanded(
            child: AudioRecordingWaveform(
              amplitude: amplitude,
              duration: duration,
              showTimer: widget.showTimer,
              color: color,
              height: widget.waveformHeight,
              showCountdownWarning: isInCountdown,
              countdownSeconds: countdownSeconds,
            ),
          ),

          const SizedBox(width: 8),

          // Small complete button or spinner
          processingState == AudioProcessingState.processing
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: color,
                  ),
                )
              : IconButton(
                  icon: Icon(
                    Icons.check,
                    color: color,
                    size: 20,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onComplete,
                ),
        ],
      ),
    );
  }
}
