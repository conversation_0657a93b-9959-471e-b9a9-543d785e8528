import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/enums.dart';
import 'package:voji/services/auth/auth_providers.dart';
import 'package:voji/services/tour/tour_service.dart';
import 'package:voji/ui/providers/auth_provider.dart';
import 'package:voji/ui/providers/color_filter_provider.dart';
import 'package:voji/ui/providers/combined_filter_provider.dart';
import 'package:voji/ui/providers/group_provider.dart';
import 'package:voji/ui/providers/passcode_provider.dart';
import 'package:voji/ui/providers/sort_provider.dart';
import 'package:voji/ui/screens/passcode_screen.dart';
import 'package:voji/ui/screens/sign_in_screen.dart';
import 'package:voji/ui/screens/user_profile_screen.dart';
import 'package:voji/ui/theme/theme_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/feature_flags.dart';
import 'package:voji/utils/logger.dart';

/// Context menu for the app
class ContextMenu extends ConsumerWidget {
  /// Constructor
  const ContextMenu({super.key});

  /// Show the passcode change screen
  void _showChangePasscodeScreen(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      builder: (bottomSheetContext) => SizedBox(
        height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
        child: PasscodeScreen(
          isChangingPasscode: true,
          onSuccess: () {
            Navigator.of(bottomSheetContext).pop(); // Close the passcode screen
          },
          onCancel: () {
            Navigator.of(bottomSheetContext).pop(); // Close the passcode screen
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(
          color: VojiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sign in or profile option
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);
              final user = ref.watch(currentUserProvider);

              if (isSignedIn && user != null) {
                // User is signed in, show profile
                return ListTile(
                  leading: user.photoUrl != null
                      ? CircleAvatar(
                          radius: 14,
                          backgroundImage: NetworkImage(user.photoUrl!),
                        )
                      : Icon(
                          Icons.person,
                          color: Theme.of(context).iconTheme.color,
                        ),
                  title: Text(
                    user.displayName,
                    style: VojiTheme.textStylesOf(context).bodyMedium,
                  ),
                  onTap: () {
                    // Close the menu
                    Navigator.of(context).pop();

                    // Navigate to user profile screen
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const UserProfileScreen(),
                      ),
                    );
                  },
                );
              } else {
                // User is not signed in, show sign in option
                return ListTile(
                  leading: Icon(
                    Icons.person,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  title: Text(
                    'Sign in',
                    style: VojiTheme.textStylesOf(context).bodyMedium,
                  ),
                  onTap: () {
                    // Close the menu
                    Navigator.of(context).pop();

                    // Navigate to sign in screen
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SignInScreen(),
                      ),
                    );
                  },
                );
              }
            },
          ),

          // Divider after sign in
          Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),

          // Theme toggle
          ListTile(
            leading: Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: Theme.of(context).iconTheme.color,
            ),
            title: Text(
              isDarkMode ? 'Switch to Light Theme' : 'Switch to Dark Theme',
              style: VojiTheme.textStylesOf(context).bodyMedium,
            ),
            onTap: () {
              // Toggle theme
              ref.read(themeModeProvider.notifier).toggleTheme();
              // Close the menu
              Navigator.of(context).pop();
            },
          ),

          // Divider
          Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),

          // Change Passcode option
          Consumer(
            builder: (context, ref, child) {
              final isPasscodeSet = ref.watch(passcodeSetCachedProvider).isSet;

              // Only show the option if a passcode is set
              if (isPasscodeSet) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.password,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Text(
                        'Change Passcode',
                        style: VojiTheme.textStylesOf(context).bodyMedium,
                      ),
                      onTap: () {
                        // Close the menu
                        Navigator.of(context).pop();

                        // Show the passcode change screen
                        _showChangePasscodeScreen(context);
                      },
                    ),
                    Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),
                  ],
                );
              } else {
                return const SizedBox.shrink(); // Don't show anything if no passcode is set
              }
            },
          ),

          // Sort by creation date
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final sortOrder = ref.watch(ideabookSortOrderProvider);
                  final isAscending = sortOrder == SortOrder.ascending;

                  return ListTile(
                    leading: Icon(
                      Icons.sort,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Sorting Order',
                      style: VojiTheme.textStylesOf(context).bodyMedium,
                    ),
                    subtitle: Text(
                      isAscending ? 'Oldest first' : 'Newest first',
                      style: VojiTheme.textStylesOf(context).bodySmall,
                    ),
                    trailing: Icon(
                      isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle sort order using the notifier method
                      Logger.debug('Toggling sort order from ${sortOrder.name}');
                      ref.read(ideabookSortOrderProvider.notifier).toggleSortOrder();

                      // Force a rebuild of the ideabooks list
                      ref.invalidate(sortOrderLoggerProvider);

                      // Force a refresh of the combined filtered ideabooks provider
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),
            ],
          ),

          // Group by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final isGroupByColorEnabled = ref.watch(groupByColorProvider);

                  return ListTile(
                    leading: Icon(
                      Icons.color_lens,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Group by Color',
                      style: VojiTheme.textStylesOf(context).bodyMedium,
                    ),
                    trailing: Icon(
                      isGroupByColorEnabled ? Icons.check_box : Icons.check_box_outline_blank,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle group by color using the notifier method
                      Logger.debug('Toggling group by color from $isGroupByColorEnabled to ${!isGroupByColorEnabled}');
                      ref.read(groupByColorProvider.notifier).toggleGroupByColor();

                      // Force a refresh of the combined filtered ideabooks provider
                      Logger.debug('Invalidating combinedFilteredIdeabooksProvider');
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Force a rebuild of the group by color logger
                      ref.invalidate(groupByColorLoggerProvider);

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),
            ],
          ),

          // Filter by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                leading: Icon(
                  Icons.filter_list,
                  color: Theme.of(context).iconTheme.color,
                  size: 20,
                ),
                title: Text(
                  'Filter by Color',
                  style: VojiTheme.textStylesOf(context).bodyMedium,
                ),
              ),

              // Color options in a single row
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: [
                    // Color options
                    for (final color in IdeabookColor.values)
                      if (color != IdeabookColor.none) // Skip 'none' color
                        GestureDetector(
                          onTap: () {
                            // Set color filter
                            ref.read(colorFilterProvider.notifier).state = color;
                            // Close the menu
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: VojiTheme.getIdeabookColor(context, color.index),
                              border: Border.all(
                                color: VojiTheme.colorsOf(context).border,
                                width: 1,
                              ),
                            ),
                          ),
                        ),
                  ],
                ),
              ),
            ],
          ),

          // Debug section (only shown in debug mode)
          if (FeatureFlags.showDebugOptions)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Divider(height: 1, thickness: 1, color: VojiTheme.colorsOf(context).divider),

                // Reset onboarding tour - the only debug option we're keeping
                ListTile(
                  leading: Icon(
                    Icons.restart_alt,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  title: Text(
                    'Clear Tour States',
                    style: VojiTheme.textStylesOf(context).bodyMedium,
                  ),
                  onTap: () {
                    // Store the context before the async gap
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    // Close the menu first
                    Navigator.of(context).pop();

                    // Then reset all tours
                    TourService.resetAllTours().then((result) {
                      // Show a snackbar to confirm
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            result
                              ? 'Tour tooltip states cleared. Tooltips will appear again when their conditions are met.'
                              : 'Failed to clear tour tooltip states'
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    });
                  },
                ),
              ],
            ),
        ],
      ),
    );
  }
}
