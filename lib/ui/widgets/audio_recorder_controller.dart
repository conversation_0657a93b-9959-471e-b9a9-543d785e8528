import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:voji/models/audio_recording_state.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/ui/providers/recording_mode_provider.dart';
import 'package:voji/utils/logger.dart';

/// Callback for when recording is completed
typedef OnRecordingCompleted = void Function(String filePath, Duration duration);

/// Callback for when recording is cancelled
typedef OnRecordingCancelled = void Function();

/// Callback for when recording fails
typedef OnRecordingFailed = void Function(String errorMessage);

/// Callback for when permission is denied
typedef OnPermissionDenied = void Function();

/// A controller widget that manages audio recording state and permissions
/// This widget doesn't have a UI - it just provides recording functionality
/// to its child widget
class AudioRecorderController extends ConsumerStatefulWidget {
  /// The child widget that will be rendered
  final Widget Function(
    BuildContext context,
    RecordingState recordingState,
    double amplitude,
    Duration duration,
    VoidCallback onCancel,
    VoidCallback onComplete,
    {bool isInCountdown,
    int? countdownSeconds}
  ) builder;

  /// Callback when recording is completed
  final OnRecordingCompleted? onRecordingCompleted;

  /// Callback when recording is cancelled
  final OnRecordingCancelled? onRecordingCancelled;

  /// Callback when recording fails
  final OnRecordingFailed? onRecordingFailed;

  /// Callback when permission is denied
  final OnPermissionDenied? onPermissionDenied;

  /// Whether to automatically start recording when the widget is initialized
  final bool autoStart;

  /// Whether to automatically exit recording mode when recording is completed or cancelled
  final bool autoExitRecordingMode;

  /// Constructor
  const AudioRecorderController({
    super.key,
    required this.builder,
    this.onRecordingCompleted,
    this.onRecordingCancelled,
    this.onRecordingFailed,
    this.onPermissionDenied,
    this.autoStart = true,
    this.autoExitRecordingMode = true,
  });

  @override
  ConsumerState<AudioRecorderController> createState() => _AudioRecorderControllerState();
}

class _AudioRecorderControllerState extends ConsumerState<AudioRecorderController> {
  // Flag to track if the recording completion has already been handled
  bool _completionHandled = false;

  @override
  void initState() {
    super.initState();
    Logger.debug('AudioRecorderController: initializing');

    // Start recording after the widget is built if autoStart is true
    if (widget.autoStart) {
      Future.microtask(() {
        Logger.debug('AudioRecorderController: auto-starting recording');
        _startRecording();
      });
    }
  }

  @override
  void dispose() {
    // Safely stop recording without using ref
    _safelyStopRecording();
    super.dispose();
  }

  /// Safely stop recording without using ref (for dispose)
  void _safelyStopRecording() {
    try {
      // Get the recording service directly to stop recording
      final recordingService = ref.read(audioRecordingServiceProvider);
      if (recordingService.isRecording) {
        recordingService.stopRecording();
      }
    } catch (e) {
      // Ignore errors during disposal
    }
  }

  /// Start the audio recording
  Future<void> _startRecording() async {
    Logger.debug('AudioRecorderController: starting recording');

    // Reset the completion handled flag when starting a new recording
    _completionHandled = false;

    try {
      // Check permission directly for debugging
      final recordingService = ref.read(audioRecordingServiceProvider);
      final hasPermission = await recordingService.checkPermission();
      final isIOS = Platform.isIOS;

      if (!hasPermission) {
        Logger.debug('AudioRecorderController: no permission, requesting...');

        // On iOS, we might need special handling due to permission_handler behavior
        if (isIOS) {
          Logger.debug('AudioRecorderController: iOS detected, special permission handling');
        }

        final status = await recordingService.requestPermission();
        Logger.debug('AudioRecorderController: permission request result: ${status.name}');

        // If permission is denied, call the callback and exit recording mode
        if (!status.isGranted) {
          Logger.debug('AudioRecorderController: permission denied, exiting recording mode');

          if (widget.onPermissionDenied != null) {
            widget.onPermissionDenied!();
          }

          if (widget.autoExitRecordingMode) {
            ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
          }
          return;
        }
      }

      final recordingStateNotifier = ref.read(audioRecordingStateProvider.notifier);
      final result = await recordingStateNotifier.startRecording();

      Logger.debug('AudioRecorderController: recording started: $result');

      // If recording failed to start, exit recording mode
      if (!result) {
        Logger.debug('AudioRecorderController: recording failed to start');

        final recordingState = ref.read(audioRecordingStateProvider);
        final errorMessage = recordingState.errorMessage ?? 'Failed to start recording';

        if (widget.onRecordingFailed != null) {
          widget.onRecordingFailed!(errorMessage);
        }

        if (widget.autoExitRecordingMode) {
          ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
        }
      }
    } catch (e) {
      Logger.error('AudioRecorderController: error starting recording', e);

      if (widget.onRecordingFailed != null) {
        widget.onRecordingFailed!('Error starting recording: $e');
      }

      // Exit recording mode on error
      if (widget.autoExitRecordingMode) {
        ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
      }
    }
  }

  /// Complete the recording and process the result
  Future<void> _completeRecording() async {
    // If completion has already been handled, don't process it again
    if (_completionHandled) {
      Logger.debug('AudioRecorderController: Recording completion already handled, ignoring duplicate call');
      return;
    }

    try {
      // Mark as handled to prevent duplicate processing
      _completionHandled = true;

      final recordingStateNotifier = ref.read(audioRecordingStateProvider.notifier);
      final success = await recordingStateNotifier.stopRecording();

      if (success) {
        final recordingState = ref.read(audioRecordingStateProvider);
        if (recordingState.filePath != null && widget.onRecordingCompleted != null) {
          Logger.debug('AudioRecorderController: Calling onRecordingCompleted callback from _completeRecording');
          widget.onRecordingCompleted!(
            recordingState.filePath!,
            recordingState.duration,
          );
        }
      } else {
        final recordingState = ref.read(audioRecordingStateProvider);
        final errorMessage = recordingState.errorMessage ?? 'Failed to complete recording';

        if (widget.onRecordingFailed != null) {
          widget.onRecordingFailed!(errorMessage);
        }
      }

      // Exit recording mode if configured to do so
      if (widget.autoExitRecordingMode) {
        ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
      }
    } catch (e) {
      // Handle any errors during completion
      Logger.error('Error completing recording', e);

      if (widget.onRecordingFailed != null) {
        widget.onRecordingFailed!('Error completing recording: $e');
      }

      // Still try to exit recording mode
      if (widget.autoExitRecordingMode) {
        try {
          ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
        } catch (_) {
          // Ignore if this fails too
        }
      }
    }
  }

  /// Cancel the recording
  Future<void> _cancelRecording() async {
    // Mark as handled to prevent any completion callbacks
    _completionHandled = true;

    try {
      final recordingStateNotifier = ref.read(audioRecordingStateProvider.notifier);
      // Use cancelRecording instead of stopRecording to properly cancel the recording
      await recordingStateNotifier.cancelRecording();

      if (widget.onRecordingCancelled != null) {
        widget.onRecordingCancelled!();
      }

      // Exit recording mode if configured to do so
      if (widget.autoExitRecordingMode) {
        ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
      }
    } catch (e) {
      // Handle any errors during cancellation
      Logger.error('Error cancelling recording', e);

      if (widget.onRecordingFailed != null) {
        widget.onRecordingFailed!('Error cancelling recording: $e');
      }

      // Still try to exit recording mode
      if (widget.autoExitRecordingMode) {
        try {
          ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
        } catch (_) {
          // Ignore if this fails too
        }
      }
    }
  }

  // Track the previous recording state to detect auto-stop
  RecordingState? _previousRecordingState;

  @override
  Widget build(BuildContext context) {
    // Watch the recording state to get amplitude updates
    final recordingState = ref.watch(audioRecordingStateProvider);

    // Check if recording was auto-stopped due to time limit
    if (_previousRecordingState == RecordingState.recording &&
        recordingState.state == RecordingState.completed &&
        !_completionHandled) {
      Logger.debug('AudioRecorderController: Recording auto-stopped due to time limit, triggering completion handler');

      // Use a microtask to avoid triggering during build
      Future.microtask(() {
        // Mark as handled to prevent duplicate processing
        _completionHandled = true;

        if (recordingState.filePath != null && widget.onRecordingCompleted != null) {
          Logger.debug('AudioRecorderController: Calling onRecordingCompleted callback from auto-stop handler');
          widget.onRecordingCompleted!(
            recordingState.filePath!,
            recordingState.duration,
          );
        }

        // Exit recording mode if configured to do so
        if (widget.autoExitRecordingMode) {
          ref.read(recordingModeStateProvider.notifier).state = RecordingModeState.inactive;
        }
      });
    }

    // Update previous state for next comparison
    _previousRecordingState = recordingState.state;

    return widget.builder(
      context,
      recordingState.state,
      recordingState.amplitude,
      recordingState.duration,
      _cancelRecording,
      _completeRecording,
      isInCountdown: recordingState.isInCountdown,
      countdownSeconds: recordingState.countdownSeconds,
    );
  }
}
