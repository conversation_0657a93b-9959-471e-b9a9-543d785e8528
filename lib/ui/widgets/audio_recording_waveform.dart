import 'package:flutter/material.dart';
import 'package:voji/constants.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/waveform_visualization.dart';

/// A widget that displays a waveform visualization with a timer
/// for audio recording
class AudioRecordingWaveform extends StatelessWidget {
  /// The current amplitude value (0.0 to 1.0)
  final double amplitude;

  /// The current recording duration
  final Duration duration;

  /// Whether to show the timer
  final bool showTimer;

  /// The color of the waveform
  final Color? color;

  /// The height of the waveform container
  final double height;

  /// The sensitivity of the waveform
  final double sensitivity;

  /// Whether to show the countdown warning instead of the waveform
  final bool showCountdownWarning;

  /// The seconds remaining in the countdown (only used when showCountdownWarning is true)
  final int? countdownSeconds;

  /// Constructor
  const AudioRecordingWaveform({
    super.key,
    required this.amplitude,
    required this.duration,
    this.showTimer = false,
    this.color,
    this.height = 40,
    this.sensitivity = 1.2,
    this.showCountdownWarning = false,
    this.countdownSeconds,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = color ?? VojiTheme.colorsOf(context).textPrimary;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Show either the waveform or the countdown warning
        if (showCountdownWarning && countdownSeconds != null)
          _buildCountdownWarning(context, themeColor)
        else
          // Waveform visualization
          WaveformVisualization(
            amplitude: amplitude,
            color: themeColor,
            height: height,
            sensitivity: sensitivity,
          ),

        // Timer display
        if (showTimer && !showCountdownWarning)
          Positioned(
            top: 0,
            right: 8,
            child: _buildTimer(context, themeColor),
          ),
      ],
    );
  }

  /// Build the countdown warning display
  Widget _buildCountdownWarning(BuildContext context, Color color) {
    return Container(
      height: height,
      width: double.infinity,
      alignment: Alignment.center,
      child: Text(
        'Will stop in ${countdownSeconds ?? 0} seconds',
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Build the timer display
  Widget _buildTimer(BuildContext context, Color color) {
    // Format the duration as mm:ss
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    final formattedDuration = '$minutes:$seconds';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        formattedDuration,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
