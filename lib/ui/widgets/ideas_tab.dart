import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/combined_idea_provider.dart';
import 'package:voji/ui/providers/firestore_idea_listener_controller.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/reorderable_ideas_list.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';

/// Widget for displaying the Ideas tab in the ideabook detail screen
class IdeasTab extends ConsumerStatefulWidget {
  /// The ideabook to display ideas for
  final Ideabook ideabook;

  /// Constructor
  const IdeasTab({
    super.key,
    required this.ideabook,
  });

  @override
  ConsumerState<IdeasTab> createState() => _IdeasTabState();
}

class _IdeasTabState extends ConsumerState<IdeasTab> {
  // Store the controller reference as a class field
  FirestoreIdeasListenerController? _firestoreController;

  @override
  void initState() {
    super.initState();

    // Start listening to Firestore updates when the tab is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Logger.debug('Starting Firestore ideas listener in IdeasTab for ideabook ${widget.ideabook.id}');
        // Store the controller reference for later use in dispose
        _firestoreController = ref.read(firestoreIdeasListenerControllerProvider(widget.ideabook.id).notifier);
        _firestoreController?.startListening();
      }
    });
  }

  @override
  void dispose() {
    // Don't stop the listener when the tab is hidden
    // The listener pool will handle TTL-based cleanup
    // Just clear the reference
    _firestoreController = null;
    super.dispose();
  }

  /// Build loading indicator
  Widget _buildLoadingIndicator(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// Build error indicator
  Widget _buildErrorIndicator(BuildContext context, Object error) {
    // Sanitize the error message
    final sanitizedError = ErrorUtils.sanitizeErrorMessage(error);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: VojiTheme.colorsOf(context).error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading ideas',
            style: VojiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            sanitizedError,
            style: VojiTheme.textStylesOf(context).bodySmall,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Refresh the ideas using the notifier
              if (_firestoreController != null) {
                _firestoreController!.startListening();
              } else {
                // If controller is not available, create it
                _firestoreController = ref.read(firestoreIdeasListenerControllerProvider(widget.ideabook.id).notifier);
                _firestoreController?.startListening();
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get ideas for this ideabook using the combined notifier provider for real-time updates
    final ideasProvider = ref.watch(combinedIdeasNotifierProvider(widget.ideabook.id));
    final ideasAsync = ref.watch(ideasProvider);

    return Scaffold(
      body: ideasAsync.when(
        loading: () => _buildLoadingIndicator(context),
        error: (error, _) => _buildErrorIndicator(context, error),
        data: (ideas) {
          if (ideas.isEmpty) {
            // If there are no ideas, show empty state
            return SizedBox(
              height: MediaQuery.of(context).size.height - 100, // Approximate height for centering
              child: _buildEmptyState(context),
            );
          }

          // If there are ideas, show the reorderable list of ideas
          return ReorderableIdeasList(
            ideabook: widget.ideabook,
            ideas: ideas,
          );
        },
      ),

    );
  }

  /// Build empty state when there are no ideas
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: VojiTheme.colorsOf(context).textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No ideas yet',
            style: VojiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the "New Idea" button to add your first idea',
            style: VojiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
