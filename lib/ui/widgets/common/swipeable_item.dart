import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/ui/theme/voji_theme.dart';

/// A callback for building the context menu that appears when swiped
typedef ContextMenuBuilder = Widget Function(BuildContext context, double totalMenuWidth);

/// A callback for building the main content of the swipeable item
typedef ContentBuilder = Widget Function(BuildContext context);

/// A callback for building the edit mode content
typedef EditModeBuilder = Widget Function(BuildContext context);

/// A generic swipeable item widget that can be used for any swipeable content
class SwipeableItem extends ConsumerStatefulWidget {
  /// The ID of this item, used to track swipe state
  final String itemId;

  /// Provider that tracks which item is currently swiped
  final StateProvider<String?> swipedItemProvider;

  /// Provider that tracks which item is currently in edit mode (optional)
  final StateProvider<String?>? editItemProvider;

  /// Builder for the context menu that appears when swiped
  final ContextMenuBuilder contextMenuBuilder;

  /// Builder for the main content
  final ContentBuilder contentBuilder;

  /// Builder for the edit mode content (optional)
  final EditModeBuilder? editModeBuilder;

  /// Maximum width of the context menu when fully swiped
  final double maxSwipeExtent;

  /// Threshold to determine when to complete the swipe
  final double swipeThreshold;

  /// Whether to show a divider at the bottom
  final bool showDivider;

  /// Constructor
  const SwipeableItem({
    super.key,
    required this.itemId,
    required this.swipedItemProvider,
    this.editItemProvider,
    required this.contextMenuBuilder,
    required this.contentBuilder,
    this.editModeBuilder,
    this.maxSwipeExtent = 120.0,
    this.swipeThreshold = 80.0,
    this.showDivider = true,
  });

  @override
  ConsumerState<SwipeableItem> createState() => _SwipeableItemState();
}

class _SwipeableItemState extends ConsumerState<SwipeableItem> with TickerProviderStateMixin {
  // Animation controller for the swipe effect
  late AnimationController _swipeController;

  // Drag gesture variables
  double _dragExtent = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _swipeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Check if this item is already in swipe mode and set initial animation value
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final swipedId = ref.read(widget.swipedItemProvider);
      if (swipedId == widget.itemId) {
        // If already swiped, set animation to completed state
        _swipeController.value = 1.0;
      }
    });

    // Listen to swipe controller to update provider state
    _swipeController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        // Animation completed to closed state, update provider
        if (ref.read(widget.swipedItemProvider) == widget.itemId) {
          ref.read(widget.swipedItemProvider.notifier).state = null;
        }
      } else if (status == AnimationStatus.completed) {
        // Animation completed to open state, update provider
        ref.read(widget.swipedItemProvider.notifier).state = widget.itemId;
      }
    });
  }

  @override
  void didUpdateWidget(SwipeableItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the item ID changed, we need to update the animation state
    if (widget.itemId != oldWidget.itemId) {
      // Check if this item is in swipe mode
      final swipedId = ref.read(widget.swipedItemProvider);
      final isSwiped = swipedId == widget.itemId;

      // Update animation value based on swipe state
      if (isSwiped && _swipeController.value != 1.0) {
        _swipeController.value = 1.0;
      } else if (!isSwiped && _swipeController.value != 0.0) {
        _swipeController.value = 0.0;
      }
    }
  }

  @override
  void dispose() {
    _swipeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Create slide animation here to avoid using MediaQuery in initState
    final slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(-widget.maxSwipeExtent / MediaQuery.of(context).size.width, 0),
    ).animate(CurvedAnimation(
      parent: _swipeController,
      curve: Curves.easeInOut,
    ));

    // Check if this item is in swipe mode
    final swipedId = ref.watch(widget.swipedItemProvider);
    final isSwiped = swipedId == widget.itemId;

    // Check if this item is in edit mode (if edit mode is supported)
    bool isEditing = false;
    if (widget.editItemProvider != null) {
      final editingId = ref.watch(widget.editItemProvider!);
      isEditing = editingId == widget.itemId;
    }

    // Trigger animations based on state changes - only if animation is not running
    // This prevents animation interruptions during user interaction
    if (isSwiped && _swipeController.value == 0 && !_swipeController.isAnimating) {
      _swipeController.forward();
    } else if (!isSwiped && _swipeController.value == 1 && !_swipeController.isAnimating) {
      _swipeController.reverse();
    }

    // If in edit mode, show the edit UI
    if (isEditing && widget.editModeBuilder != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            padding: const EdgeInsets.all(16.0),
            child: widget.editModeBuilder!(context),
          ),
          if (widget.showDivider)
            Divider(height: 1, thickness: 0.5, color: VojiTheme.colorsOf(context).divider),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          // Handle horizontal drag for swipe effect
          onHorizontalDragStart: (details) {
            // Reset drag extent at the start of a new drag
            _dragExtent = isSwiped ? widget.maxSwipeExtent : 0;
          },
          onHorizontalDragUpdate: (details) {
            // Only allow right-to-left swipe (negative drag) when not swiped
            // Or left-to-right swipe (positive drag) when already swiped
            setState(() {
              _dragExtent -= details.primaryDelta!;
              _dragExtent = _dragExtent.clamp(0.0, widget.maxSwipeExtent);

              // Update animation value based on drag extent
              _swipeController.value = (_dragExtent / widget.maxSwipeExtent).clamp(0.0, 1.0);
            });
          },
          onHorizontalDragEnd: (details) {
            // Determine whether to complete or reverse the swipe based on velocity and position
            final velocity = details.primaryVelocity ?? 0;

            // If velocity is significant, use it to determine direction
            if (velocity.abs() > 200) {
              if (velocity < 0) {
                // Swiping left (opening)
                _swipeController.forward();
                ref.read(widget.swipedItemProvider.notifier).state = widget.itemId;
              } else {
                // Swiping right (closing)
                _swipeController.reverse();
                ref.read(widget.swipedItemProvider.notifier).state = null;
              }
            } else {
              // Otherwise use position threshold
              if (_dragExtent >= widget.swipeThreshold) {
                _swipeController.forward();
                ref.read(widget.swipedItemProvider.notifier).state = widget.itemId;
              } else {
                _swipeController.reverse();
                ref.read(widget.swipedItemProvider.notifier).state = null;
              }
            }
          },
          // Handle tap on row to restore normal state
          onTap: isSwiped ? () {
            // Only handle swipe state here
            _swipeController.reverse();
            ref.read(widget.swipedItemProvider.notifier).state = null;
          } : null,
          child: Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: ClipRect(
              child: Stack(
                children: [
                  // Context menu (fixed position, revealed when main row slides)
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: widget.contextMenuBuilder(context, widget.maxSwipeExtent),
                  ),

                  // Main row content with slide animation
                  SlideTransition(
                    position: slideAnimation,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      color: Theme.of(context).scaffoldBackgroundColor,
                      alignment: Alignment.centerLeft,
                      child: widget.contentBuilder(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (widget.showDivider)
          Divider(height: 1, thickness: 0.5, color: VojiTheme.colorsOf(context).divider),
      ],
    );
  }
}
