import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:waveform_flutter/waveform_flutter.dart';

/// Configuration for the waveform visualization
class WaveformConfig {
  /// The color of the waveform
  final Color color;

  /// The height of the waveform container
  final double height;

  /// The maximum height of the waveform bars
  final int barMaxHeight;

  /// The sensitivity of the waveform (higher values make small sounds more visible)
  final double sensitivity;

  /// Whether to show the timer
  final bool showTimer;

  /// Whether to show the countdown warning instead of the waveform
  final bool showCountdownWarning;

  /// The seconds remaining in the countdown (only used when showCountdownWarning is true)
  final int? countdownSeconds;

  /// Creates a new WaveformConfig
  const WaveformConfig({
    required this.color,
    this.height = 40,
    this.barMaxHeight = 3,
    this.sensitivity = 1.0,
    this.showTimer = false,
    this.showCountdownWarning = false,
    this.countdownSeconds,
  });

  /// Default configuration
  static WaveformConfig defaultConfig(BuildContext context) {
    return WaveformConfig(
      color: VojiTheme.colorsOf(context).textPrimary,
      height: 40,
      barMaxHeight: 3,
      sensitivity: 1.0,
      showTimer: false,
      showCountdownWarning: false,
    );
  }
}

/// A reusable waveform visualization widget
class ReusableWaveform extends StatefulWidget {
  /// The current amplitude value (0.0 to 1.0)
  final double? amplitude;

  /// Stream of amplitude values (0.0 to 1.0)
  final Stream<double>? amplitudeStream;

  /// The duration of the recording
  final Duration? duration;

  /// Configuration for the waveform
  final WaveformConfig config;

  /// Constructor
  const ReusableWaveform({
    super.key,
    this.amplitude,
    this.amplitudeStream,
    this.duration,
    required this.config,
  }) : assert(amplitude != null || amplitudeStream != null,
             'Either amplitude or amplitudeStream must be provided');

  @override
  State<ReusableWaveform> createState() => _ReusableWaveformState();
}

class _ReusableWaveformState extends State<ReusableWaveform> {
  final StreamController<Amplitude> _amplitudeController = StreamController<Amplitude>.broadcast();
  StreamSubscription<double>? _amplitudeStreamSubscription;

  @override
  void initState() {
    super.initState();

    // If amplitude is provided directly, update the waveform
    if (widget.amplitude != null) {
      _updateAmplitude(widget.amplitude!);
    }

    // If amplitude stream is provided, subscribe to it
    if (widget.amplitudeStream != null) {
      _amplitudeStreamSubscription = widget.amplitudeStream!.listen(_updateAmplitude);
    }
  }

  @override
  void didUpdateWidget(ReusableWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If amplitude is provided directly and has changed, update the waveform
    if (widget.amplitude != null && widget.amplitude != oldWidget.amplitude) {
      _updateAmplitude(widget.amplitude!);
    }

    // If amplitude stream has changed, resubscribe
    if (widget.amplitudeStream != oldWidget.amplitudeStream) {
      _amplitudeStreamSubscription?.cancel();
      if (widget.amplitudeStream != null) {
        _amplitudeStreamSubscription = widget.amplitudeStream!.listen(_updateAmplitude);
      }
    }
  }

  /// Update the waveform with a new amplitude value
  void _updateAmplitude(double value) {
    // Scale and amplify the value based on sensitivity
    final amplifiedValue = _amplifyAmplitude(value);

    _amplitudeController.add(
      Amplitude(
        current: amplifiedValue,
        max: 100,
      ),
    );
  }

  /// Converts the amplitude value to the range expected by the waveform package
  /// Takes a value between 0.0 and 1.0 and returns a value between 0.0 and 100.0
  double _amplifyAmplitude(double value) {
    // Apply sensitivity factor to make the visualization more responsive
    double amplifiedValue = value * widget.config.sensitivity;

    // Ensure value stays in 0-1 range after sensitivity is applied
    amplifiedValue = amplifiedValue.clamp(0.0, 1.0);

    // Scale to 0-100 range for the waveform package
    return (amplifiedValue * 100).clamp(0.0, 100.0);
  }

  @override
  void dispose() {
    _amplitudeStreamSubscription?.cancel();
    _amplitudeController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Show either the waveform or the countdown warning
        if (widget.config.showCountdownWarning && widget.config.countdownSeconds != null)
          _buildCountdownWarning(context)
        else
          // Waveform visualization
          SizedBox(
            height: widget.config.height,
            width: double.infinity,
            child: AnimatedWaveList(
              stream: _amplitudeController.stream,
              barBuilder: (animation, amplitude) {
                return WaveFormBar(
                  animation: animation,
                  amplitude: amplitude,
                  color: widget.config.color,
                  maxHeight: widget.config.barMaxHeight,
                );
              },
            ),
          ),

        // Timer display
        if (widget.config.showTimer && widget.duration != null && !widget.config.showCountdownWarning)
          Positioned(
            top: 0,
            right: 8,
            child: _buildTimer(context),
          ),
      ],
    );
  }

  /// Build the countdown warning display
  Widget _buildCountdownWarning(BuildContext context) {
    return Container(
      height: widget.config.height,
      width: double.infinity,
      alignment: Alignment.center,
      child: Text(
        'Will stop in ${widget.config.countdownSeconds ?? 0} seconds',
        style: TextStyle(
          color: widget.config.color,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Build the timer display
  Widget _buildTimer(BuildContext context) {
    final minutes = widget.duration!.inMinutes;
    final seconds = widget.duration!.inSeconds % 60;

    return Text(
      '$minutes:${seconds.toString().padLeft(2, '0')}',
      style: TextStyle(
        color: widget.config.color,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

/// A consumer widget wrapper for the reusable waveform
class ReusableWaveformConsumer extends ConsumerWidget {
  /// The current amplitude value (0.0 to 1.0)
  final double? amplitude;

  /// Stream of amplitude values (0.0 to 1.0)
  final Stream<double>? amplitudeStream;

  /// The duration of the recording
  final Duration? duration;

  /// Configuration for the waveform
  final WaveformConfig? config;

  /// Constructor
  const ReusableWaveformConsumer({
    super.key,
    this.amplitude,
    this.amplitudeStream,
    this.duration,
    this.config,
  }) : assert(amplitude != null || amplitudeStream != null,
             'Either amplitude or amplitudeStream must be provided');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final effectiveConfig = config ?? WaveformConfig.defaultConfig(context);

    return ReusableWaveform(
      amplitude: amplitude,
      amplitudeStream: amplitudeStream,
      duration: duration,
      config: effectiveConfig,
    );
  }
}
