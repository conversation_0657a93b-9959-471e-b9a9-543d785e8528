// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCEAPzjsx4aUKqNcOs3hsfv1YXIjMTKXf8',
    appId: '1:821877445690:web:f829b65e596d2e08eed103',
    messagingSenderId: '821877445690',
    projectId: 'voji-backend',
    authDomain: 'voji-backend.firebaseapp.com',
    storageBucket: 'voji-backend.firebasestorage.app',
    measurementId: 'G-61SWRW02S2',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAboKmRgmEhVWVkI1cV1xvL49Spz8vnkkA',
    appId: '1:821877445690:android:6ca2f72ef15d522beed103',
    messagingSenderId: '821877445690',
    projectId: 'voji-backend',
    storageBucket: 'voji-backend.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCAFNJMVXep9vxO0Yzqonkb_3FaTJ94DC0',
    appId: '1:821877445690:ios:5f3053c96b81d620eed103',
    messagingSenderId: '821877445690',
    projectId: 'voji-backend',
    storageBucket: 'voji-backend.firebasestorage.app',
    iosBundleId: 'com.blackstickyrice.voji',
  );

}