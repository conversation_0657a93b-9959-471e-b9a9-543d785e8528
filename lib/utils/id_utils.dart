import 'package:voji/utils/logger.dart';

/// Utility functions for generating and working with document IDs
class IdUtils {
  /// Characters used for encoding (A-Za-z0-9)
  static const String _chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  /// Base for encoding (number of characters in _chars)
  static const int _base = 62;

  /// Generate a timestamp-based ID
  ///
  /// Uses the current timestamp in milliseconds and encodes it to a shorter string
  static String generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return encodeTimestamp(timestamp);
  }

  /// Encode a timestamp to a shorter string
  ///
  /// Uses base62 encoding (A-Za-z0-9) to make the ID shorter
  static String encodeTimestamp(int timestamp) {
    try {
      if (timestamp <= 0) {
        Logger.error('Invalid timestamp: $timestamp');
        return '';
      }

      String encoded = '';
      int value = timestamp;

      while (value > 0) {
        encoded = _chars[value % _base] + encoded;
        value ~/= _base;
      }

      return encoded;
    } catch (e) {
      Logger.error('Error encoding timestamp', e);
      return '';
    }
  }

  /// Decode a timestamp from an encoded string
  ///
  /// Converts the base62 encoded string back to a timestamp
  static int decodeTimestamp(String encoded) {
    try {
      int decoded = 0;

      for (int i = 0; i < encoded.length; i++) {
        decoded = decoded * _base + _chars.indexOf(encoded[i]);
      }

      return decoded;
    } catch (e) {
      Logger.error('Error decoding timestamp', e);
      return 0;
    }
  }

  /// Convert a timestamp to a DateTime
  static DateTime timestampToDateTime(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  /// Convert an encoded timestamp to a DateTime
  static DateTime encodedToDateTime(String encoded) {
    final timestamp = decodeTimestamp(encoded);
    return timestampToDateTime(timestamp);
  }

  /// Encode a color value to a single byte character
  ///
  /// Maps IdeabookColor enum index to a single character:
  /// - 0 (none) -> '0'
  /// - 1 (red) -> '1'
  /// - 2 (green) -> '2'
  /// - 3 (blue) -> '3'
  /// - 4 (yellow) -> '4'
  /// - 5 (purple) -> '5'
  /// - 6 (orange) -> '6'
  /// - Any other value -> '0'
  static String encodeColor(int colorIndex) {
    if (colorIndex >= 0 && colorIndex <= 6) {
      return colorIndex.toString();
    }
    return '0'; // Default to none (0) for invalid values
  }

  /// Decode a color value from a single byte character
  ///
  /// Maps a single character to IdeabookColor enum index:
  /// - '0' -> 0 (none)
  /// - '1' -> 1 (red)
  /// - '2' -> 2 (green)
  /// - '3' -> 3 (blue)
  /// - '4' -> 4 (yellow)
  /// - '5' -> 5 (purple)
  /// - '6' -> 6 (orange)
  /// - Any other value -> 0 (none)
  static int decodeColor(String encoded) {
    try {
      final colorValue = int.parse(encoded);
      if (colorValue >= 0 && colorValue <= 6) {
        return colorValue;
      }
      return 0; // Default to none (0) for invalid values
    } catch (e) {
      Logger.error('Error decoding color', e);
      return 0; // Default to none (0) for invalid values
    }
  }
}
