import 'dart:async';
import 'package:voji/utils/logger.dart';

/// A utility class for retrying operations with exponential backoff
class RetryUtil {
  /// Retry a function with exponential backoff
  /// 
  /// [operation] - The function to retry
  /// [maxRetries] - The maximum number of retries (default: 3)
  /// [initialDelay] - The initial delay in milliseconds (default: 500ms)
  /// [maxDelay] - The maximum delay in milliseconds (default: 5000ms)
  /// [factor] - The exponential factor for backoff (default: 2)
  /// [operationName] - Optional name for logging purposes
  /// 
  /// Returns the result of the operation if successful, or throws the last error if all retries fail
  static Future<T> retry<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    int initialDelay = 500,
    int maxDelay = 5000,
    double factor = 2.0,
    String? operationName,
  }) async {
    int attempt = 0;
    int delay = initialDelay;
    final opName = operationName ?? 'operation';

    while (true) {
      attempt++;
      try {
        Logger.debug('RetryUtil: Attempting $opName (attempt $attempt of ${maxRetries + 1})');
        final result = await operation();
        if (attempt > 1) {
          Logger.debug('RetryUtil: $opName succeeded after $attempt attempts');
        }
        return result;
      } catch (e) {
        if (attempt > maxRetries) {
          Logger.error('RetryUtil: $opName failed after ${maxRetries + 1} attempts', e);
          rethrow;
        }
        
        // Calculate next delay with exponential backoff
        delay = (delay * factor).round();
        if (delay > maxDelay) {
          delay = maxDelay;
        }
        
        Logger.debug('RetryUtil: $opName failed (attempt $attempt), retrying in ${delay}ms: $e');
        await Future.delayed(Duration(milliseconds: delay));
      }
    }
  }

  /// Retry a function with exponential backoff, with custom error handling
  /// 
  /// [operation] - The function to retry
  /// [shouldRetry] - A function that determines if a retry should be attempted based on the error
  /// [maxRetries] - The maximum number of retries (default: 3)
  /// [initialDelay] - The initial delay in milliseconds (default: 500ms)
  /// [maxDelay] - The maximum delay in milliseconds (default: 5000ms)
  /// [factor] - The exponential factor for backoff (default: 2)
  /// [operationName] - Optional name for logging purposes
  /// 
  /// Returns the result of the operation if successful, or throws the last error if all retries fail
  static Future<T> retryWithCondition<T>({
    required Future<T> Function() operation,
    required bool Function(Object error) shouldRetry,
    int maxRetries = 3,
    int initialDelay = 500,
    int maxDelay = 5000,
    double factor = 2.0,
    String? operationName,
  }) async {
    int attempt = 0;
    int delay = initialDelay;
    final opName = operationName ?? 'operation';

    while (true) {
      attempt++;
      try {
        Logger.debug('RetryUtil: Attempting $opName (attempt $attempt of ${maxRetries + 1})');
        final result = await operation();
        if (attempt > 1) {
          Logger.debug('RetryUtil: $opName succeeded after $attempt attempts');
        }
        return result;
      } catch (e) {
        if (attempt > maxRetries || !shouldRetry(e)) {
          if (!shouldRetry(e)) {
            Logger.error('RetryUtil: $opName failed with non-retryable error', e);
          } else {
            Logger.error('RetryUtil: $opName failed after ${maxRetries + 1} attempts', e);
          }
          rethrow;
        }
        
        // Calculate next delay with exponential backoff
        delay = (delay * factor).round();
        if (delay > maxDelay) {
          delay = maxDelay;
        }
        
        Logger.debug('RetryUtil: $opName failed (attempt $attempt), retrying in ${delay}ms: $e');
        await Future.delayed(Duration(milliseconds: delay));
      }
    }
  }
}
