import 'package:voji/models/enums.dart';
import 'package:voji/utils/logger.dart';

/// Utility functions for working with colors
class ColorUtils {
  /// Convert a color string to an IdeabookColor enum
  ///
  /// If the color string is not recognized, returns IdeabookColor.none
  static IdeabookColor stringToIdeabookColor(String? colorString, {String logPrefix = ''}) {
    if (colorString == null) {
      Logger.debug('${logPrefix}No color provided, using default');
      return IdeabookColor.none;
    }

    // Map the color string to the enum
    IdeabookColor result;
    switch (colorString.toLowerCase()) {
      case 'red':
        result = IdeabookColor.red;
        break;
      case 'green':
        result = IdeabookColor.green;
        break;
      case 'blue':
        result = IdeabookColor.blue;
        break;
      case 'yellow':
        result = IdeabookColor.yellow;
        break;
      case 'orange':
        result = IdeabookColor.orange;
        break;
      case 'purple':
        result = IdeabookColor.purple;
        break;
      default:
        Logger.debug('${logPrefix}Unrecognized color: $colorString, using default');
        result = IdeabookColor.none;
        break;
    }

    Logger.debug('${logPrefix}Converted color string "$colorString" to ${result.name} (index: ${result.index})');
    return result;
  }
}
