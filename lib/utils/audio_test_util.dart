import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/audio_recording_state.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/utils/logger.dart';

/// A utility class for testing audio recording functionality
class AudioTestUtil {
  /// Test the audio recording functionality
  static Future<void> testAudioRecording(WidgetRef ref) async {
    final recordingStateNotifier = ref.read(audioRecordingStateProvider.notifier);
    final fileManager = ref.read(audioFileManagerProvider);
    
    try {
      Logger.debug('Starting audio recording test...');
      
      // Start recording
      final started = await recordingStateNotifier.startRecording();
      if (!started) {
        Logger.error('Failed to start recording');
        return;
      }
      
      Logger.debug('Recording started. Recording for 3 seconds...');
      
      // Record for 3 seconds
      await Future.delayed(const Duration(seconds: 3));
      
      // Stop recording
      final stopped = await recordingStateNotifier.stopRecording();
      if (!stopped) {
        Logger.error('Failed to stop recording');
        return;
      }
      
      // Get the recording state
      final state = ref.read(audioRecordingStateProvider);
      if (state.state != RecordingState.completed || state.filePath == null) {
        Logger.error('Recording not completed or file path is null');
        return;
      }
      
      Logger.debug('Recording completed. File path: ${state.filePath}');
      Logger.debug('Recording duration: ${state.duration.inMilliseconds}ms');
      
      // Check if the file exists
      final file = File(state.filePath!);
      final exists = await file.exists();
      Logger.debug('File exists: $exists');
      
      if (exists) {
        // Get the file size
        final size = await fileManager.getFileSize(state.filePath!);
        Logger.debug('File size: $size bytes');
      }
      
      // List all recordings
      final recordings = await fileManager.listRecordings();
      Logger.debug('Total recordings: ${recordings.length}');
      
      // Reset the state
      recordingStateNotifier.resetState();
      
      Logger.debug('Audio recording test completed successfully');
    } catch (e) {
      Logger.error('Error during audio recording test', e);
    }
  }
}
