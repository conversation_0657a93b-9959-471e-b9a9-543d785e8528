import 'dart:convert';
import 'package:voji/models/chat_message.dart';
import 'package:voji/services/firebase/cloud_function_service.dart';
import 'package:voji/utils/logger.dart';

/// Response from a chat request
class ChatResponse {
  /// Whether the request was successful
  final bool isSuccess;

  /// The content of the response
  final String? content;

  /// The error message if the request failed
  final String? errorMessage;

  /// The original JSON response from the LLM
  final Map<String, dynamic>? originalJson;

  /// Constructor for successful response
  const ChatResponse.success({
    this.content,
    this.originalJson,
  })  : isSuccess = true,
        errorMessage = null;

  /// Constructor for failed response
  const ChatResponse.failure({
    required this.errorMessage,
  })  : isSuccess = false,
        content = null,
        originalJson = null;
}

/// Enum for message roles
enum MessageRole {
  /// User message
  user,

  /// Assistant message
  assistant,
}

/// Service for interacting with Gemini 2.0 Flash Lite model for chat functionality
class GeminiChatService {
  /// The model to use for chat
  final String model;

  /// The Cloud Function service
  final CloudFunctionService _cloudFunctionService;

  /// Constructor
  GeminiChatService({
    this.model = 'gemini-2.0-flash',
    CloudFunctionService? cloudFunctionService,
  }) : _cloudFunctionService = cloudFunctionService ?? CloudFunctionService();

  /// Dispose resources
  void dispose() {
    // No resources to dispose
  }

  /// Ensure proper encoding of the content
  String _ensureProperEncoding(String content) {
    try {
      // Try to decode and re-encode to ensure proper UTF-8 handling
      return utf8.decode(utf8.encode(content));
    } catch (e) {
      Logger.error('Error ensuring proper encoding', e);
      return content; // Return original if encoding fails
    }
  }

  /// Send a chat request to the Gemini API via Firebase Cloud Function
  /// Limits the number of messages sent to the LLM to the last 25 messages
  /// to save LLM token input and reduce API costs
  Future<ChatResponse> chat(List<ChatMessage> messages) async {
    try {
      Logger.debug('======= GEMINI CHAT SERVICE DEBUGGING =======');
      Logger.debug('Sending chat request to Gemini API via Firebase Cloud Function');
      Logger.debug('Received ${messages.length} messages for chat');

      // Check if we have any messages
      if (messages.isEmpty) {
        Logger.debug('CRITICAL ERROR: No messages provided to Gemini chat!');
        return ChatResponse.failure(
          errorMessage: 'No messages provided to chat',
        );
      }

      // Limit to the last 25 messages to save LLM token input and API cost
      final limitedMessages = messages.length > 25
          ? messages.sublist(messages.length - 25)
          : messages;

      Logger.debug('Using ${limitedMessages.length} messages after limiting');

      // Format messages for the API - Gemini uses a different format than Gemma
      final formattedParts = limitedMessages.map((message) {
        // Convert from ChatMessage.MessageRole to our local MessageRole enum
        final isUserMessage = message.role.toString() == 'MessageRole.user';
        return {
          'role': isUserMessage ? 'user' : 'model',
          'parts': [
            {'text': message.content}
          ]
        };
      }).toList();

      // Log the messages for debugging
      if (messages.length > 25) {
        Logger.debug('Limited chat history from ${messages.length} to 25 messages to save LLM token input and API cost');
      }

      // Check if the first message contains the ideas section
      if (limitedMessages.isNotEmpty) {
        // Check if it's a user message
        final isUserMessage = limitedMessages[0].role.toString() == 'MessageRole.user';
        if (isUserMessage) {
          final content = limitedMessages[0].content;
          Logger.debug('Checking first message for Ideas section:');
          if (content.contains('Ideas captured:')) {
            Logger.debug('First message contains Ideas section, this is likely a context message');
          }
        }
      }

      // Create the request body for Gemini API
      final requestBody = {
        'contents': formattedParts,
        'model': model,
        'generationConfig': {
          'temperature': 0.9, // Increased temperature for more creative responses in chat
        },
      };

      Logger.debug('Gemini API request body: $requestBody');

      // Call the Firebase Cloud Function
      final response = await _cloudFunctionService.callLlmApi(
        provider: 'gemini',
        requestBody: requestBody,
      );

      if (!response.isSuccess || response.data == null) {
        Logger.error('Gemini API error via Firebase Cloud Function: ${response.errorMessage}');

        // Log detailed error information if available
        if (response.details != null) {
          final location = response.details!['location'] as String? ?? 'unknown';
          final timestamp = response.details!['timestamp'] as String? ?? '';
          final stack = response.details!['stack'] as String? ?? '';

          Logger.error('Error details - Location: $location, Time: $timestamp');
          Logger.error('Stack trace: $stack');
        }

        return ChatResponse.failure(
          errorMessage: response.errorMessage ?? 'Unknown error',
        );
      }

      // Parse the response
      final jsonResponse = response.data;
      Logger.debug('Gemini API response via Firebase Cloud Function: $jsonResponse');

      // Extract the message content
      final rawContent = jsonResponse['candidates'][0]['content']['parts'][0]['text'];

      // Ensure proper encoding of the content
      // This helps with non-ASCII characters like Chinese
      final decodedContent = _ensureProperEncoding(rawContent);

      // Try to parse the JSON response from the LLM
      // Initialize with the decoded content as fallback
      String finalContent = decodedContent;

      // First, check if the content is wrapped in markdown code blocks
      String contentToProcess = decodedContent;
      if (contentToProcess.trim().startsWith('```') && contentToProcess.trim().endsWith('```')) {
        // Extract content between code blocks
        final startIndex = contentToProcess.indexOf('```') + 3;
        final endIndex = contentToProcess.lastIndexOf('```');

        // Check if we have valid indices and the content between them
        if (startIndex < endIndex && startIndex < contentToProcess.length) {
          // Skip the language identifier if present (e.g., ```json)
          int adjustedStartIndex = startIndex;
          if (contentToProcess.substring(startIndex).contains('\n')) {
            adjustedStartIndex = contentToProcess.indexOf('\n', startIndex) + 1;
          }

          contentToProcess = contentToProcess.substring(adjustedStartIndex, endIndex).trim();
        }
      }

      // Try to parse as JSON
      Map<String, dynamic>? parsedJson;
      bool foundJson = false;

      try {
        if (contentToProcess.trim().startsWith('{') && contentToProcess.trim().endsWith('}')) {
          parsedJson = json.decode(contentToProcess) as Map<String, dynamic>?;
          foundJson = parsedJson != null;

          if (foundJson) {
            if (parsedJson.containsKey('response')) {
              finalContent = parsedJson['response'] as String? ?? finalContent;
              Logger.debug('Successfully extracted response from JSON');
            } else if (parsedJson.containsKey('user_prompt') && parsedJson.containsKey('response')) {
              finalContent = parsedJson['response'] as String? ?? finalContent;
              Logger.debug('Successfully extracted response from JSON with user_prompt');
            }
          }
        }
      } catch (e) {
        Logger.error('Error parsing JSON response', e);
        // Keep using the decodedContent as fallback
      }

      Logger.debug('Successfully received response from Gemini API');
      Logger.debug('Final response content length: ${finalContent.length} characters');
      Logger.debug('======= END GEMINI CHAT SERVICE DEBUGGING =======');

      return ChatResponse.success(
        content: finalContent,
        originalJson: parsedJson,
      );
    } catch (e) {
      Logger.error('Error in Gemini chat request via Firebase Cloud Function', e);

      // Create a more detailed error message
      String errorMessage = 'Error in chat request: $e';

      // Check for specific error types and provide more helpful messages
      if (e.toString().contains('timeout')) {
        errorMessage = 'The request timed out. Please try again.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      return ChatResponse.failure(
        errorMessage: errorMessage,
      );
    }
  }

  /// Generate suggested prompts based on ideabook content
  Future<ChatResponse> generateSuggestedPrompts(String promptContent) async {
    try {
      Logger.debug('======= GEMINI SUGGESTED PROMPTS DEBUGGING =======');
      Logger.debug('Generating suggested prompts with Gemini API');

      // Create a single message with the prompt content
      final formattedParts = [
        {
          'role': 'user',
          'parts': [
            {'text': promptContent}
          ]
        }
      ];

      // Create the request body for Gemini API
      final requestBody = {
        'contents': formattedParts,
        'model': model,
        'generationConfig': {
          'temperature': 0.9, // Increased temperature for more creative and diverse prompt suggestions
        },
      };

      Logger.debug('Gemini API request body for suggested prompts: $requestBody');

      // Call the Firebase Cloud Function
      final response = await _cloudFunctionService.callLlmApi(
        provider: 'gemini',
        requestBody: requestBody,
      );

      if (!response.isSuccess || response.data == null) {
        Logger.error('Gemini API error for suggested prompts: ${response.errorMessage}');
        return ChatResponse.failure(
          errorMessage: response.errorMessage ?? 'Unknown error',
        );
      }

      // Parse the response
      final jsonResponse = response.data;
      Logger.debug('Gemini API response for suggested prompts: $jsonResponse');

      // Extract the message content
      final rawContent = jsonResponse['candidates'][0]['content']['parts'][0]['text'];

      // Ensure proper encoding of the content
      final decodedContent = _ensureProperEncoding(rawContent);

      Logger.debug('Successfully received suggested prompts from Gemini API');
      Logger.debug('Raw response content: $decodedContent');

      // Process the content to extract JSON if needed
      String processedContent = decodedContent;

      // Check for markdown code blocks with ```json or ``` pattern
      if (processedContent.trim().startsWith('```')) {
        Logger.debug('Content appears to be wrapped in markdown code blocks, extracting...');

        // Extract content between code blocks
        final startIndex = processedContent.indexOf('```') + 3;
        final endIndex = processedContent.lastIndexOf('```');

        // Check if we have valid indices and the content between them
        if (startIndex < endIndex && startIndex < processedContent.length) {
          // Skip the language identifier if present (e.g., ```json)
          int adjustedStartIndex = startIndex;
          if (processedContent.substring(startIndex).contains('\n')) {
            adjustedStartIndex = processedContent.indexOf('\n', startIndex) + 1;
          }

          processedContent = processedContent.substring(adjustedStartIndex, endIndex).trim();
          Logger.debug('Extracted content from markdown code blocks');
        }
      }

      // Try to parse as JSON
      Map<String, dynamic>? parsedJson;
      bool foundJson = false;

      try {
        if (processedContent.trim().startsWith('{') && processedContent.trim().endsWith('}')) {
          parsedJson = json.decode(processedContent) as Map<String, dynamic>?;
          foundJson = parsedJson != null;
          Logger.debug('Successfully parsed JSON from content');
        }
      } catch (e) {
        Logger.error('Error parsing JSON from content', e);
        // Keep using the processed content as is
      }

      Logger.debug('======= END GEMINI SUGGESTED PROMPTS DEBUGGING =======');

      // If we successfully parsed JSON, return it as is
      // The SuggestedPromptsProvider will handle extracting the prompts
      if (foundJson && parsedJson != null) {
        return ChatResponse.success(
          content: processedContent,
          originalJson: parsedJson
        );
      }

      // Otherwise return the processed content
      return ChatResponse.success(content: processedContent);
    } catch (e) {
      Logger.error('Error generating suggested prompts', e);
      return ChatResponse.failure(
        errorMessage: 'Error generating suggested prompts: $e',
      );
    }
  }
}
