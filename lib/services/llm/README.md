# LLM Service

This module provides LLM (Large Language Model) integration for the Voji app.

## Components

### LlmService

The core interface that defines the LLM operations:

- Transcribing audio to text
- (Future) Refining and summarizing text
- (Future) Chat functionality

### GeminiService

Implementation of the LLM service using Google's Gemini API:

- Handles audio transcription using the gemini-2.0-flash-lite model
- Processes audio files and sends them to the API
- Uses structured output format for reliable JSON responses
- Handles both inline audio data and file references

### Providers

- `geminiServiceProvider`: Provides an instance of the GeminiService with the API key
- `llmServiceProvider`: Provides the current LLM service implementation (currently Gemini)

## Usage

### Transcribing Audio

```dart
// Get the LLM service
final llmService = ref.read(llmServiceProvider);

// Transcribe audio file
final result = await llmService.transcribeAudio(audioFilePath);

if (result.isSuccess) {
  // Use the transcription
  final shortName = result.shortName;
  final color = result.color;
} else {
  // Handle error
  print('Transcription failed: ${result.errorMessage}');
}
```

## API Keys

API keys are hardcoded in the providers as per the requirements to embed them in the app binary.

## Implementation Details

### JSON Response Format

The service uses Gemini's JSON response capability by:

1. Specifying the JSON schema in the prompt text
2. Setting `response_mime_type` to `application/json` in the generation config

The prompt includes the following schema specification:

```
Output in JSON format:
{
  "short_name": "short name of the ideabook. <= 10 words. no repeat 'ideabook'",
  "color": "color of the ideabook. one of red, green, blue, yellow, orange, purple"
}
```

### Response Parsing

The service handles multiple response formats from the Gemini API:

1. JSON array format: `[{"short_name": "...", "color": "..."}]`
2. Direct JSON object: `{"short_name": "...", "color": "..."}`

The parser automatically detects the format and extracts the required fields. This makes the service more robust to variations in the API response format.

## Future Enhancements

- Add support for Grok API for chat functionality
- Implement text refinement and summarization
- Add support for longer audio files using the File API
