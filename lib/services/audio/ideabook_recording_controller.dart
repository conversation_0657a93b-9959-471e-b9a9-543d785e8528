import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/audio/audio_recording_controller.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/ui/providers/bottom_panel_notification_provider.dart';
import 'package:voji/ui/providers/ideabook_provider.dart';
import 'package:voji/ui/providers/recording_mode_provider.dart';
import 'package:voji/ui/widgets/bottom_panel_controller.dart';
import 'package:voji/utils/color_utils.dart';
import 'package:voji/utils/logger.dart';

/// Controller for recording audio for new ideabooks
class IdeabookRecordingController extends BaseAudioRecordingController {
  @override
  String get controllerName => 'IdeabookRecordingController';

  @override
  Future<void> handleRecordingCompleted(WidgetRef ref, String filePath, Duration duration) async {
    Logger.debug('$controllerName: Recording completed: $filePath, duration: ${duration.inSeconds}s');

    // Set the state to processing
    beginAudioProcessing(ref);
    safelyUseRef(ref, (r) {
      r.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;
    });

    try {
      // Transcribe the audio
      Logger.debug('$controllerName: Transcribing audio...');
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error('$controllerName: Could not access LLM service - widget may be disposed');
        return;
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdeabook,
      );

      if (transcriptionResult.isSuccess && transcriptionResult.shortName != null) {
        // Create a new ideabook with the transcription
        Logger.debug('$controllerName: Creating new ideabook with name: ${transcriptionResult.shortName}');

        final ideabooksNotifier = safelyUseRef(ref, (r) => r.read(ideabooksNotifierProvider.notifier));
        if (ideabooksNotifier == null) {
          Logger.error('$controllerName: Could not access ideabooks notifier - widget may be disposed');
          return;
        }

        // Convert the color string to IdeabookColor enum
        final ideabookColor = ColorUtils.stringToIdeabookColor(
          transcriptionResult.color,
          logPrefix: '$controllerName: '
        );

        if (transcriptionResult.color != null) {
          Logger.debug('$controllerName: Using LLM-selected color: ${ideabookColor.name}');
        }

        await ideabooksNotifier.createIdeabook(
          name: transcriptionResult.shortName!,
          color: ideabookColor,
          onCreated: () {
            // Set the flag to trigger scrolling to the bottom
            safelyUseRef(ref, (r) {
              r.read(newIdeabookCreatedProvider.notifier).state = true;
            });
          },
        );

        // Show success notification
        showNotification(ref, 'Ideabook created successfully');
      } else {
        // Transcription failed, do NOT create a document to save Firestore writes
        Logger.error('$controllerName: Transcription failed: ${transcriptionResult.errorMessage}');

        // Show error notification
        showNotification(ref, 'Transcription failed - please try again');
      }
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('$controllerName: Error processing recording', e);

      // Do NOT create a document on error to save Firestore writes
      // Show error notification
      showNotification(ref, 'Error processing recording - please try again');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Reset recording mode
      safelyUseRef(ref, (r) {
        r.read(recordingModeProvider.notifier).state = false;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled');

    // Reset recording mode
    safelyUseRef(ref, (r) {
      r.read(recordingModeProvider.notifier).state = false;
    });

    // Return to normal state
    safelyUseRef(ref, (r) {
      r.read(bottomPanelStateProvider.notifier).state = BottomPanelState.normal;
    });
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error('$controllerName: Recording failed: $errorMessage');

    // Reset recording mode
    safelyUseRef(ref, (r) {
      r.read(recordingModeProvider.notifier).state = false;
    });

    // Show notification for error
    safelyUseRef(ref, (r) {
      r.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;
    });

    showNotification(ref, 'Recording failed');
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error('$controllerName: Microphone permission denied');

    // Reset recording mode
    safelyUseRef(ref, (r) {
      r.read(recordingModeProvider.notifier).state = false;
    });

    // Show notification for permission error
    safelyUseRef(ref, (r) {
      r.read(bottomPanelStateProvider.notifier).state = BottomPanelState.notification;
    });

    showNotification(ref, 'Microphone permission required');
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Use the bottom panel notification provider to show notifications
    safelyUseRef(ref, (r) {
      r.read(showBottomPanelNotificationProvider)(message);
    });
  }
}

/// Provider for the ideabook recording controller
final ideabookRecordingControllerProvider = Provider<IdeabookRecordingController>((ref) {
  return IdeabookRecordingController();
});
