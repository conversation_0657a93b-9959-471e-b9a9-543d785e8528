import 'dart:async';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:voji/constants.dart';
import 'package:voji/models/audio_recording_state.dart';
import 'package:voji/services/audio/audio_file_manager.dart';
import 'package:voji/services/audio/audio_recording_service.dart';
import 'package:voji/utils/logger.dart';

/// Provider for the audio recording service
final audioRecordingServiceProvider = Provider<AudioRecordingService>((ref) {
  final service = AudioRecordingService();

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the audio file manager
final audioFileManagerProvider = Provider<AudioFileManager>((ref) {
  return AudioFileManager();
});

/// Provider for the current audio recording state
final audioRecordingStateProvider = StateNotifierProvider<AudioRecordingStateNotifier, AudioRecordingState>((ref) {
  final recordingService = ref.watch(audioRecordingServiceProvider);
  return AudioRecordingStateNotifier(recordingService);
});

/// Notifier for the audio recording state
class AudioRecordingStateNotifier extends StateNotifier<AudioRecordingState> {
  final AudioRecordingService _recordingService;
  StreamSubscription<double>? _amplitudeSubscription;
  Timer? _durationTimer;

  /// Creates a new AudioRecordingStateNotifier
  AudioRecordingStateNotifier(this._recordingService) : super(AudioRecordingState.initial()) {
    // Subscribe to amplitude updates
    _amplitudeSubscription = _recordingService.amplitudeStream.listen(_onAmplitudeUpdate);
  }

  @override
  void dispose() {
    _amplitudeSubscription?.cancel();
    _durationTimer?.cancel();
    super.dispose();
  }

  /// Start recording
  Future<bool> startRecording() async {
    Logger.debug('AudioRecordingStateNotifier: startRecording called, current state: ${state.state}');

    if (state.state == RecordingState.recording) {
      Logger.debug('AudioRecordingStateNotifier: already recording, ignoring');
      return true; // Already recording
    }

    try {
      // Check permission
      Logger.debug('AudioRecordingStateNotifier: checking microphone permission');
      final hasPermission = await _recordingService.checkPermission();
      final isIOS = Platform.isIOS;

      if (!hasPermission) {
        Logger.debug('AudioRecordingStateNotifier: no permission, requesting...');

        // On iOS, we might need special handling due to permission_handler behavior
        if (isIOS) {
          Logger.debug('AudioRecordingStateNotifier: iOS detected, special permission handling');
        }

        final permissionStatus = await _recordingService.requestPermission();

        if (!permissionStatus.isGranted) {
          Logger.error('AudioRecordingStateNotifier: microphone permission denied (${permissionStatus.name})');
          state = AudioRecordingState.failed(
            errorMessage: 'Microphone permission denied',
          );
          return false;
        }

        Logger.debug('AudioRecordingStateNotifier: permission granted');
      }

      // Start recording
      Logger.debug('AudioRecordingStateNotifier: starting recording service');
      final filePath = await _recordingService.startRecording();

      if (filePath == null) {
        Logger.error('AudioRecordingStateNotifier: recording service returned null file path');
        state = AudioRecordingState.failed(
          errorMessage: 'Failed to start recording',
        );
        return false;
      }

      Logger.debug('AudioRecordingStateNotifier: recording started at $filePath');

      // Update state
      state = AudioRecordingState.recording(
        filePath: filePath,
        duration: Duration.zero,
        amplitude: 0.0,
      );

      // Start duration timer
      Logger.debug('AudioRecordingStateNotifier: starting duration timer');
      _startDurationTimer();

      return true;
    } catch (e) {
      Logger.error('AudioRecordingStateNotifier: failed to start recording', e);
      state = AudioRecordingState.failed(
        errorMessage: 'Failed to start recording: $e',
      );
      return false;
    }
  }

  /// Stop recording
  Future<bool> stopRecording() async {
    if (state.state != RecordingState.recording) {
      return false; // Not recording
    }

    try {
      // Stop recording
      final result = await _recordingService.stopRecording();

      // Cancel duration timer
      _durationTimer?.cancel();
      _durationTimer = null;

      if (!result.isSuccess) {
        state = AudioRecordingState.failed(
          errorMessage: result.errorMessage ?? 'Failed to stop recording',
        );
        return false;
      }

      // Update state
      state = AudioRecordingState.completed(
        filePath: result.filePath,
        duration: result.duration,
      );

      return true;
    } catch (e) {
      Logger.error('Failed to stop recording', e);
      state = AudioRecordingState.failed(
        errorMessage: 'Failed to stop recording: $e',
      );
      return false;
    }
  }

  /// Cancel recording
  Future<bool> cancelRecording() async {
    if (state.state != RecordingState.recording) {
      return false; // Not recording
    }

    try {
      // Cancel recording
      final success = await _recordingService.cancelRecording();

      // Cancel duration timer
      _durationTimer?.cancel();
      _durationTimer = null;

      // Reset state
      state = AudioRecordingState.initial();

      return success;
    } catch (e) {
      Logger.error('Failed to cancel recording', e);
      state = AudioRecordingState.failed(
        errorMessage: 'Failed to cancel recording: $e',
      );
      return false;
    }
  }

  /// Reset the recording state
  void resetState() {
    state = AudioRecordingState.initial();
  }

  // Counter for amplitude logging
  int _amplitudeLogCounter = 0;

  /// Handle amplitude updates
  void _onAmplitudeUpdate(double amplitude) {
    if (state.state == RecordingState.recording) {
      // Log every 20th amplitude update (about every 2 seconds)
      if (_amplitudeLogCounter % 20 == 0) {
        Logger.debug('AudioRecordingStateNotifier: amplitude update: $amplitude');
      }
      _amplitudeLogCounter++;

      state = state.withAmplitude(amplitude);
    }
  }

  /// Start a timer to update the duration
  void _startDurationTimer() {
    // Cancel any existing timer
    _durationTimer?.cancel();

    // Start a new timer that fires every 100ms
    _durationTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (state.state == RecordingState.recording) {
        final newDuration = state.duration + const Duration(milliseconds: 100);
        state = state.withDuration(newDuration);

        // Check if we're approaching the maximum recording duration for countdown warning
        final secondsRemaining = kMaxAudioRecordingDurationSeconds - newDuration.inSeconds;

        if (secondsRemaining <= kRecordingCountdownWarningSeconds && secondsRemaining > 0) {
          // We're in the countdown phase
          if (!state.isInCountdown) {
            Logger.debug('AudioRecordingStateNotifier: Entering countdown phase, ${secondsRemaining}s remaining');
          }

          // Update the state with countdown information
          state = state.withCountdown(
            isInCountdown: true,
            countdownSeconds: secondsRemaining,
          );
        }

        // Check if we've reached the maximum recording duration
        if (newDuration.inSeconds >= kMaxAudioRecordingDurationSeconds) {
          Logger.debug('AudioRecordingStateNotifier: Maximum recording duration reached (${kMaxAudioRecordingDurationSeconds}s), stopping recording automatically');

          // Store the current file path before stopping
          final currentFilePath = state.filePath;

          // Stop the recording automatically
          stopRecording().then((success) {
            if (success) {
              Logger.debug('AudioRecordingStateNotifier: Recording auto-stopped successfully after reaching time limit');

              // Notify listeners that the recording was auto-stopped by setting a special flag in the state
              if (currentFilePath != null) {
                state = state.copyWith(
                  autoStopped: true,
                  filePath: currentFilePath,
                );

                Logger.debug('AudioRecordingStateNotifier: Set autoStopped flag to true for file: $currentFilePath');
              }
            } else {
              Logger.error('AudioRecordingStateNotifier: Failed to auto-stop recording after reaching time limit');
            }
          });
        }
      } else {
        // Cancel the timer if we're not recording
        timer.cancel();
        _durationTimer = null;
      }
    });
  }
}
