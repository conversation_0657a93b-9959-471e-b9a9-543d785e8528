import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/audio/audio_recording_controller.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/ui/providers/audio_providers.dart';
import 'package:voji/utils/logger.dart';

/// Controller for handling audio recording for chat input
class ChatRecordingController extends BaseAudioRecordingController {
  /// Constructor
  ChatRecordingController();

  @override
  String get controllerName => 'ChatRecordingController';

  @override
  Future<void> handleRecordingCompleted(
    WidgetRef ref,
    String filePath,
    Duration duration,
  ) async {
    Logger.debug('$controllerName: Recording completed, transcribing audio...');

    // Set the processing state to show the spinner
    beginAudioProcessing(ref);

    try {
      // Use the LLM service to transcribe the audio for chat input
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error('$controllerName: Could not access LLM service - widget may be disposed');
        return;
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.chatInput,
      );

      if (transcriptionResult.isSuccess && transcriptionResult.transcript != null) {
        // Return the transcribed text to be added to the chat input
        Logger.debug('$controllerName: Transcription successful: ${transcriptionResult.transcript}');

        // Notify listeners about the transcription result
        safelyUseRef(ref, (r) {
          r.read(chatTranscriptionResultProvider.notifier).state = transcriptionResult.transcript;
        });
      } else {
        // Handle transcription failure
        Logger.error('$controllerName: Transcription failed: ${transcriptionResult.errorMessage}');

        // Set empty result to indicate failure
        safelyUseRef(ref, (r) {
          r.read(chatTranscriptionResultProvider.notifier).state = null;
        });

        // Show notification
        showNotification(ref, 'Transcription failed');
      }
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('$controllerName: Error processing recording', e);

      // Set empty result to indicate failure
      safelyUseRef(ref, (r) {
        r.read(chatTranscriptionResultProvider.notifier).state = null;
      });

      // Show notification
      showNotification(ref, 'Error processing recording');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Always exit recording mode after completion
      safelyUseRef(ref, (r) {
        r.read(chatRecordingModeProvider.notifier).state = false;
      });

      Logger.debug('$controllerName: Exited recording mode after completion');
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled');

    // Reset the transcription result
    safelyUseRef(ref, (r) {
      r.read(chatTranscriptionResultProvider.notifier).state = null;
    });

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(chatRecordingModeProvider.notifier).state = false;
    });

    Logger.debug('$controllerName: Exited recording mode after cancellation');
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error('$controllerName: Recording failed: $errorMessage');

    // Reset the transcription result
    safelyUseRef(ref, (r) {
      r.read(chatTranscriptionResultProvider.notifier).state = null;
    });

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(chatRecordingModeProvider.notifier).state = false;
    });

    Logger.debug('$controllerName: Exited recording mode after failure');

    // Show notification
    showNotification(ref, 'Recording failed');
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error('$controllerName: Microphone permission denied');

    // Reset the transcription result
    safelyUseRef(ref, (r) {
      r.read(chatTranscriptionResultProvider.notifier).state = null;
    });

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(chatRecordingModeProvider.notifier).state = false;
    });

    Logger.debug('$controllerName: Exited recording mode after permission denial');

    // Show notification
    showNotification(ref, 'Microphone permission required');
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Chat doesn't have a specific notification mechanism, so just log it
    try {
      Logger.debug('$controllerName: $message');
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug('$controllerName: Could not show notification - widget may be disposed');
    }
  }
}

/// Provider for the chat recording controller
final chatRecordingControllerProvider = Provider<ChatRecordingController>((ref) {
  return ChatRecordingController();
});
