# Audio Recording Service

This module provides audio recording functionality for the Voji app.

## Components

### AudioRecordingService

The core service that handles audio recording operations:

- Starting and stopping recordings
- Monitoring amplitude for waveform visualization
- Managing recording files

### AudioFileManager

Utility service for managing audio files:

- Listing recordings
- Deleting recordings
- Cleaning up old recordings
- Getting file sizes

### AudioRecordingState

Model representing the state of an audio recording:

- Current state (idle, recording, completed, failed)
- File path
- Duration
- Amplitude
- Error messages

### Providers

- `audioRecordingServiceProvider`: Provides the AudioRecordingService
- `audioFileManagerProvider`: Provides the AudioFileManager
- `audioRecordingStateProvider`: Provides the current AudioRecordingState

## Usage

```dart
// Start recording
final recordingStateNotifier = ref.read(audioRecordingStateProvider.notifier);
await recordingStateNotifier.startRecording();

// Monitor recording state
final recordingState = ref.watch(audioRecordingStateProvider);
final amplitude = recordingState.amplitude; // For waveform visualization
final duration = recordingState.duration;   // For displaying recording time

// Stop recording
await recordingStateNotifier.stopRecording();

// Get the recorded file path
final filePath = recordingState.filePath;
```

## File Storage

Audio recordings are stored in the app's documents directory under a `recordings` folder. Each recording has a unique filename based on timestamp and UUID.

## Permissions

The service handles requesting microphone permissions using the permission_handler package. The necessary permissions are declared in:

- Android: AndroidManifest.xml
- iOS: Info.plist

## Testing

Use the `AudioTestUtil` class to test the audio recording functionality:

```dart
await AudioTestUtil.testAudioRecording(ref);
```
