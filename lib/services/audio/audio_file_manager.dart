import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:voji/utils/logger.dart';

/// Service for managing audio files
class AudioFileManager {
  /// Get the directory where recordings are stored
  Future<Directory> getRecordingsDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory('${directory.path}/recordings');
    
    // Create the recordings directory if it doesn't exist
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }
    
    return recordingsDir;
  }
  
  /// List all recording files
  Future<List<File>> listRecordings() async {
    final recordingsDir = await getRecordingsDirectory();
    
    try {
      final files = await recordingsDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.aac'))
          .map((entity) => entity as File)
          .toList();
      
      // Sort by creation time (newest first)
      files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
      
      return files;
    } catch (e) {
      Logger.error('Failed to list recordings', e);
      return [];
    }
  }
  
  /// Delete a recording file
  Future<bool> deleteRecording(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        Logger.debug('Deleted recording: $filePath');
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Failed to delete recording: $filePath', e);
      return false;
    }
  }
  
  /// Clean up old recordings
  /// Keeps only the specified number of most recent recordings
  Future<int> cleanupOldRecordings({int keepCount = 100}) async {
    try {
      final recordings = await listRecordings();
      
      // If we have fewer recordings than the keep count, do nothing
      if (recordings.length <= keepCount) {
        return 0;
      }
      
      // Delete the oldest recordings
      final recordingsToDelete = recordings.sublist(keepCount);
      int deletedCount = 0;
      
      for (final file in recordingsToDelete) {
        final success = await deleteRecording(file.path);
        if (success) {
          deletedCount++;
        }
      }
      
      Logger.debug('Cleaned up $deletedCount old recordings');
      return deletedCount;
    } catch (e) {
      Logger.error('Failed to clean up old recordings', e);
      return 0;
    }
  }
  
  /// Get the file size in bytes
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      Logger.error('Failed to get file size: $filePath', e);
      return 0;
    }
  }
  
  /// Get the total size of all recordings in bytes
  Future<int> getTotalRecordingsSize() async {
    try {
      final recordings = await listRecordings();
      int totalSize = 0;
      
      for (final file in recordings) {
        totalSize += await file.length();
      }
      
      return totalSize;
    } catch (e) {
      Logger.error('Failed to get total recordings size', e);
      return 0;
    }
  }
}
