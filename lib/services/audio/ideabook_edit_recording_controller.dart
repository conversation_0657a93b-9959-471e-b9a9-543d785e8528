import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/audio/audio_recording_controller.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/ui/providers/ideabook_edit_recording_provider.dart';
import 'package:voji/utils/logger.dart';

/// Controller for recording audio to append to an ideabook name in edit mode
class IdeabookEditRecordingController extends BaseAudioRecordingController {
  /// The ideabook ID for which the recording is being made
  final String ideabookId;

  /// Callback to handle the transcribed text
  final Function(String transcribedText)? onTranscriptionComplete;

  /// Flag to track whether the callback has been called
  bool _callbackCalled = false;

  /// Constructor
  IdeabookEditRecordingController({
    required this.ideabookId,
    this.onTranscriptionComplete,
  });

  @override
  String get controllerName => 'IdeabookEditRecordingController';

  /// Safely use ref
  /// This is a utility method to safely use a ref, catching any exceptions if the widget is disposed
  @override
  T? safelyUseRef<T>(WidgetRef ref, T Function(WidgetRef) action, {T? defaultValue}) {
    try {
      return action(ref);
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug('$controllerName: Could not use ref - widget may be disposed');
      return defaultValue;
    }
  }

  @override
  Future<void> handleRecordingCompleted(WidgetRef ref, String filePath, Duration duration) async {
    Logger.debug('$controllerName: Recording completed for ideabook $ideabookId: $filePath, duration: ${duration.inSeconds}s');

    // Set the state to processing
    beginAudioProcessing(ref);

    try {
      // Transcribe the audio
      Logger.debug('$controllerName: Transcribing audio...');
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error('$controllerName: Could not access LLM service - widget may be disposed');
        return;
      }

      // Get the ideabook name from the Firestore service
      String? ideabookName;
      try {
        final firestoreService = safelyUseRef(ref, (r) => r.read(firestoreServiceProvider));
        if (firestoreService != null) {
          final ideabook = await firestoreService.listenToIdeabookById(ideabookId).first;
          if (ideabook != null) {
            ideabookName = ideabook.name;
            Logger.debug('$controllerName: Using ideabook name: $ideabookName');
          }
        }
      } catch (e) {
        Logger.error('$controllerName: Error getting ideabook name', e);
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdea,
        ideabookName: ideabookName,
      );

      // Handle transcription result
      if (transcriptionResult.isSuccess && transcriptionResult.idea != null) {
        final transcribedText = transcriptionResult.idea!;
        Logger.debug('$controllerName: Transcription successful: $transcribedText');

        // Call the callback if provided and not already called
        if (onTranscriptionComplete != null && !_callbackCalled) {
          _callbackCalled = true;
          onTranscriptionComplete!(transcribedText);
        }
      } else {
        // Handle transcription failure
        Logger.error('$controllerName: Transcription failed: ${transcriptionResult.errorMessage}');
        showNotification(ref, 'Transcription failed');
      }
    } catch (e) {
      Logger.error('$controllerName: Error processing recording', e);
      showNotification(ref, 'Error processing recording');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Exit recording mode
      safelyUseRef(ref, (r) {
        r.read(ideabookEditRecordingProvider.notifier).state = null;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled for ideabook $ideabookId');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookEditRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error('$controllerName: Recording failed for ideabook $ideabookId: $errorMessage');

    // Show error notification
    showNotification(ref, 'Recording failed');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookEditRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error('$controllerName: Microphone permission denied for ideabook $ideabookId');

    // Show permission error notification
    showNotification(ref, 'Microphone permission required');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookEditRecordingProvider.notifier).state = null;
    });

    Logger.debug('$controllerName: Exited recording mode after permission denial');
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Show a snackbar notification
    safelyUseRef(ref, (r) {
      // Use a simple logger for now since we don't have a specific notification provider for ideabooks
      Logger.debug('$controllerName: $message');
    });
  }
}
