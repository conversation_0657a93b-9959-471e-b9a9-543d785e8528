import 'dart:io';
import 'package:flutter/services.dart';
import 'package:voji/utils/logger.dart';

/// Service to handle back button press on Android
class BackButtonService {
  static const MethodChannel _channel = MethodChannel('com.blackstickyrice.voji/back_button');
  static BackButtonService? _instance;

  /// Flag to check if we're running on Android
  final bool _isAndroid = Platform.isAndroid;

  /// Callback to be called when back button is pressed
  Function? _onBackPressed;

  /// Private constructor
  BackButtonService._() {
    if (_isAndroid) {
      _channel.setMethodCallHandler(_handleMethodCall);
    }
  }

  /// Get the singleton instance
  static BackButtonService get instance {
    _instance ??= BackButtonService._();
    return _instance!;
  }

  /// Handle method calls from the platform
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onBackPressed':
        if (_onBackPressed != null) {
          _onBackPressed!();
        }
        break;
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details: 'Method ${call.method} not implemented',
        );
    }
  }

  /// Set the back button handler
  /// If [isInChatOrNotesTab] is true, the back button will be intercepted
  /// and the [onBackPressed] callback will be called
  ///
  /// Note: This only has an effect on Android. On other platforms, it's a no-op.
  Future<void> setBackButtonHandler({
    required bool isInChatOrNotesTab,
    Function? onBackPressed,
  }) async {
    _onBackPressed = onBackPressed;

    // Only invoke the method channel on Android
    if (_isAndroid) {
      try {
        await _channel.invokeMethod('setBackButtonHandler', {
          'isInChatOrNotesTab': isInChatOrNotesTab,
        });
      } catch (e) {
        Logger.error('Error setting back button handler', e);
      }
    }
  }
}
