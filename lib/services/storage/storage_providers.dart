import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/storage/local_file_storage.dart';

/// Provider for the local file storage service
final localFileStorageProvider = Provider<LocalFileStorage>((ref) {
  return LocalFileStorage();
});

/// Provider for the chat file storage service
final chatFileStorageProvider = Provider<ChatFileStorage>((ref) {
  final storage = ref.watch(localFileStorageProvider);
  return ChatFileStorage(storage);
});
