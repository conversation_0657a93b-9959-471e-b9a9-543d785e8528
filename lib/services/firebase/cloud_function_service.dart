import 'package:cloud_functions/cloud_functions.dart';
import 'package:voji/utils/error_utils.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/retry_util.dart';

/// Service for interacting with Firebase Cloud Functions
class CloudFunctionService {
  /// The Firebase Functions instance
  final FirebaseFunctions _functions;

  /// Constructor
  CloudFunctionService({FirebaseFunctions? functions})
      : _functions = functions ?? FirebaseFunctions.instance;

  /// Call a Firebase Cloud Function for LLM API
  ///
  /// [provider] - The LLM provider to use (e.g., "gemini", "grok")
  /// [requestBody] - The request body to send to the LLM API
  Future<CloudFunctionResponse> callLlmApi({
    required String provider,
    required Map<String, dynamic> requestBody,
  }) async {
    Logger.debug('======= CLOUD FUNCTION SERVICE DEBUGGING =======');
    Logger.debug('Calling Firebase Cloud Function for LLM API: $provider');

    // Log the request details
    Logger.debug('Request provider: $provider');

    // Check if the request body contains content
    if (requestBody.containsKey('contents')) {
      final contents = requestBody['contents'] as List<dynamic>?;
      Logger.debug('Request contains ${contents?.length ?? 0} content items');

      // Check if there are any contents
      if (contents != null && contents.isNotEmpty) {
        // Log the first content item
        final firstContent = contents.first;
        final role = firstContent['role'] as String?;
        Logger.debug('First content role: $role');

        // Check if the first content has parts
        final parts = firstContent['parts'] as List<dynamic>?;
        if (parts != null && parts.isNotEmpty) {
          final text = parts.first['text'] as String?;
          if (text != null) {
            // Check if the text contains the Ideas section
            if (text.contains('Ideas:')) {
              final ideasIndex = text.indexOf('Ideas:');
              final userPromptIndex = text.indexOf('User\'s prompt:');

              if (ideasIndex >= 0 && userPromptIndex > ideasIndex) {
                // Extract the Ideas section
                final ideasSection = text.substring(
                  ideasIndex + 'Ideas:'.length,
                  userPromptIndex
                ).trim();

                Logger.debug('Ideas section in request:');
                Logger.debug(ideasSection);

                // Check if the Ideas section is empty
                if (ideasSection.isEmpty || ideasSection == 'No ideas found in this ideabook yet.') {
                  Logger.debug('WARNING: Ideas section in request is empty or just contains placeholder text!');
                }
              }
            } else {
              Logger.debug('WARNING: No Ideas section found in the request!');
            }
          }
        }
      }
    }

    // Use retry utility for the cloud function call with only 2 retries
    // (since the LLM API calls already have retry logic in the cloud function)
    try {
      return await RetryUtil.retry<CloudFunctionResponse>(
        operation: () => _callLlmApiInternal(provider, requestBody),
        maxRetries: 2, // Only retry 2 times as requested
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'callLlmApi ($provider)',
      );
    } catch (e) {
      Logger.error('All retries failed for LLM API call', e);

      // Check if the error is a FirebaseFunctionsException
      if (e is FirebaseFunctionsException) {
        // Extract the detailed error information
        final details = e.details as Map<String, dynamic>?;
        final location = details?['location'] as String? ?? 'unknown';
        final timestamp = details?['timestamp'] as String? ?? '';
        final stack = details?['stack'] as String? ?? '';

        // Create a detailed error message for logging
        final detailedMessage = 'Error in $location at $timestamp: ${e.message}\n$stack';
        Logger.error('Firebase Function error details: $detailedMessage');

        Logger.debug('Firebase Functions Exception: ${e.code} - ${e.message}');
        Logger.debug('======= END CLOUD FUNCTION SERVICE DEBUGGING (ERROR) =======');

        // Check if this is a permission error
        final isPermissionError = ErrorUtils.isPermissionError(e) ||
                                 (e.code == 'permission-denied') ||
                                 (e.code == 'PERMISSION_DENIED');

        if (isPermissionError) {
          // Return a generic permission denied message
          return CloudFunctionResponse.failure(
            errorMessage: ErrorUtils.permissionDeniedMessage,
            details: details,
          );
        } else {
          // Return the original error message
          return CloudFunctionResponse.failure(
            errorMessage: 'Error from Firebase Cloud Function: ${e.message}',
            details: details,
          );
        }
      }

      Logger.debug('Unexpected error type: ${e.runtimeType}');
      Logger.debug('======= END CLOUD FUNCTION SERVICE DEBUGGING (ERROR) =======');

      // Check if this is a permission error
      final isPermissionError = ErrorUtils.isPermissionError(e);

      if (isPermissionError) {
        // Return a generic permission denied message
        return CloudFunctionResponse.failure(
          errorMessage: ErrorUtils.permissionDeniedMessage,
        );
      } else {
        // Return the original error message
        return CloudFunctionResponse.failure(
          errorMessage: 'Error calling Firebase Cloud Function after retries: $e',
        );
      }
    }
  }

  /// Internal method to call the Firebase Cloud Function
  /// This is separated to allow for retry logic
  Future<CloudFunctionResponse> _callLlmApiInternal(
    String provider,
    Map<String, dynamic> requestBody,
  ) async {
    try {
      Logger.debug('Calling Firebase Cloud Function...');

      // Call the Firebase Cloud Function
      final result = await _functions.httpsCallable('callLanguageModelApi').call({
        'provider': provider,
        'requestBody': requestBody,
      });

      // Parse the response
      final data = result.data as Map<String, dynamic>;
      Logger.debug('Firebase Cloud Function response received');

      // Check if the request was successful
      final isSuccess = data['isSuccess'] as bool;
      if (isSuccess) {
        Logger.debug('Cloud Function call was successful');
        Logger.debug('======= END CLOUD FUNCTION SERVICE DEBUGGING =======');
        return CloudFunctionResponse.success(data: data['data']);
      } else {
        final errorMessage = data['errorMessage'] as String? ?? 'Unknown error';
        Logger.debug('Cloud Function call failed: $errorMessage');
        Logger.debug('======= END CLOUD FUNCTION SERVICE DEBUGGING (ERROR) =======');
        return CloudFunctionResponse.failure(
          errorMessage: errorMessage,
        );
      }
    } catch (e) {
      Logger.error('Error in internal call to Firebase Cloud Function', e);
      // Rethrow to allow the retry mechanism to work
      rethrow;
    }
  }
}

/// Response from a Firebase Cloud Function
class CloudFunctionResponse {
  /// Whether the request was successful
  final bool isSuccess;

  /// The response data
  final dynamic data;

  /// Error message if the request failed
  final String? errorMessage;

  /// Additional error details if available
  final Map<String, dynamic>? details;

  /// Creates a new CloudFunctionResponse for a successful request
  const CloudFunctionResponse.success({
    required this.data,
  })  : isSuccess = true,
        errorMessage = null,
        details = null;

  /// Creates a new CloudFunctionResponse for a failed request
  const CloudFunctionResponse.failure({
    required this.errorMessage,
    this.details,
  })  : isSuccess = false,
        data = null;
}
