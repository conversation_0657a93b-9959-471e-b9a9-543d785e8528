import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:voji/models/ideabook.dart';
import 'package:voji/models/idea.dart';
import 'package:voji/models/note.dart';
import 'package:voji/models/enums.dart';
import 'package:voji/utils/logger.dart';
import 'package:voji/utils/crypto_utils.dart';
import 'package:voji/utils/id_utils.dart';
import 'package:voji/utils/retry_util.dart';

/// Service for interacting with Firestore
class FirestoreService {
  /// Firestore instance
  final FirebaseFirestore _firestore;

  /// Auth instance to get the current user
  final FirebaseAuth _auth;

  /// Constructor
  FirestoreService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// Get the current user ID
  String? get _userId => _auth.currentUser?.uid;

  /// Get the ideabooks collection reference for the current user
  CollectionReference<Map<String, dynamic>> get _ideabooksCollection {
    if (_userId == null) {
      throw Exception('User is not signed in');
    }
    return _firestore.collection('users').doc(_userId).collection('ideabooks');
  }

  /// Get the ideas collection reference for a specific ideabook
  CollectionReference<Map<String, dynamic>> _ideasCollection(String ideabookId) {
    if (_userId == null) {
      throw Exception('User is not signed in');
    }
    return _ideabooksCollection.doc(ideabookId).collection('ideas');
  }

  /// Get the notes collection reference for a specific ideabook
  CollectionReference<Map<String, dynamic>> _notesCollection(String ideabookId) {
    if (_userId == null) {
      throw Exception('User is not signed in');
    }
    return _ideabooksCollection.doc(ideabookId).collection('notes');
  }

  /// Get the chats collection reference for a specific ideabook
  CollectionReference<Map<String, dynamic>> _chatsCollection(String ideabookId) {
    if (_userId == null) {
      throw Exception('User is not signed in');
    }
    return _ideabooksCollection.doc(ideabookId).collection('chats');
  }

  /// Convert a Firestore document to an Ideabook model
  Ideabook _convertToIdeabook(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    // Use the document metadata for timestamps
    final createdAt = doc.metadata.hasPendingWrites
        ? DateTime.now()
        : (data.containsKey('createdAt')
            ? (data['createdAt'] as Timestamp).toDate()
            : DateTime.now());
    final updatedAt = doc.metadata.hasPendingWrites
        ? DateTime.now()
        : (data.containsKey('updatedAt')
            ? (data['updatedAt'] as Timestamp).toDate()
            : DateTime.now());

    // Extract and decode the color
    final colorCode = data['c'] as String;
    Logger.debug('Converting Firestore document to ideabook:');
    Logger.debug('- Document ID: ${doc.id}');
    Logger.debug('- Raw data: $data');
    Logger.debug('- Encoded color from Firestore: "$colorCode"');

    final colorIndex = IdUtils.decodeColor(colorCode);
    Logger.debug('- Decoded color index: $colorIndex');

    final color = IdeabookColor.values[colorIndex];
    Logger.debug('- Final color: ${color.name}');

    return Ideabook(
      id: doc.id,
      name: data['n'] as String,
      color: color,
      isLocked: data['l'] as bool? ?? false,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Convert an Ideabook model to a Firestore document
  Map<String, dynamic> _convertFromIdeabook(Ideabook ideabook) {
    // Get the color index and encode it
    final colorIndex = ideabook.color.index;
    final colorCode = IdUtils.encodeColor(colorIndex);

    Logger.debug('Converting ideabook to Firestore document:');
    Logger.debug('- Color: ${ideabook.color.name}');
    Logger.debug('- Color index: $colorIndex');
    Logger.debug('- Encoded color: "$colorCode"');

    // Create the document data
    final data = {
      'n': ideabook.name,
      'c': colorCode,
      'l': ideabook.isLocked,
      // No need to set createdAt and updatedAt, Firestore handles this automatically
    };

    Logger.debug('Final document data: $data');
    return data;
  }

  /// Convert a Firestore document to an Idea model
  Idea _convertToIdea(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;

    // Get creation timestamp from the 't' field if available and not null
    // During pending writes, the server timestamp might be null
    DateTime createdAt;
    if (data.containsKey('t') && data['t'] != null) {
      // We have a valid timestamp from the server
      createdAt = (data['t'] as Timestamp).toDate();
    } else {
      // Either no 't' field or it's null (during pending writes)
      // Use current time as a fallback
      createdAt = DateTime.now();
      Logger.debug('Using current time as fallback for null server timestamp in idea ${doc.id}');
    }

    final updatedAt = doc.metadata.hasPendingWrites
        ? DateTime.now()
        : (data.containsKey('updatedAt')
            ? (data['updatedAt'] as Timestamp).toDate()
            : DateTime.now());

    // Check if the idea has a sort order field
    final double? sortOrder = data.containsKey('s') ? (data['s'] as num).toDouble() : null;

    return Idea(
      id: doc.id,
      content: data['c'] as String,
      sortOrder: sortOrder,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Convert an Idea model to a Firestore document
  Map<String, dynamic> _convertFromIdea(Idea idea, {bool isNewIdea = false}) {
    final data = <String, dynamic>{
      'c': idea.content,
    };

    // Only include the sort order field if it's not null
    if (idea.sortOrder != null) {
      data['s'] = idea.sortOrder;
    }

    // Add server timestamp for new ideas
    if (isNewIdea) {
      data['t'] = FieldValue.serverTimestamp();
    }

    return data;
  }

  /// Convert a Firestore document to a Note model
  Note _convertToNote(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;

    // Get creation timestamp from the 'r' field if available and not null
    // During pending writes, the server timestamp might be null
    DateTime createdAt;
    if (data.containsKey('r') && data['r'] != null) {
      // We have a valid timestamp from the server
      createdAt = (data['r'] as Timestamp).toDate();
    } else {
      // Either no 'r' field or it's null (during pending writes)
      // Fall back to legacy fields or use current time
      createdAt = doc.metadata.hasPendingWrites
          ? DateTime.now()
          : (data.containsKey('createdAt')
              ? (data['createdAt'] as Timestamp).toDate()
              : DateTime.now());
      Logger.debug('Using fallback for null server timestamp in note ${doc.id}');
    }

    final updatedAt = doc.metadata.hasPendingWrites
        ? DateTime.now()
        : (data.containsKey('updatedAt')
            ? (data['updatedAt'] as Timestamp).toDate()
            : DateTime.now());

    // Check if the note has a sort order field
    final double? sortOrder = data.containsKey('s') ? (data['s'] as num).toDouble() : null;

    return Note(
      id: doc.id,
      title: data['t'] as String,
      content: data['c'] as String,
      sortOrder: sortOrder,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Convert a Note model to a Firestore document
  Map<String, dynamic> _convertFromNote(Note note, {bool isNewNote = false}) {
    final data = <String, dynamic>{
      't': note.title,
      'c': note.content,
    };

    // Only include the sort order field if it's not null
    if (note.sortOrder != null) {
      data['s'] = note.sortOrder;
    }

    // Add server timestamp for new notes
    if (isNewNote) {
      data['r'] = FieldValue.serverTimestamp();
    }

    return data;
  }

  /// Start listening to ideabooks collection changes
  /// Returns a stream of ideabooks
  Stream<List<Ideabook>> listenToIdeabooks() {
    Logger.debug('Starting to listen to ideabooks collection');

    try {
      return _ideabooksCollection.snapshots().map((snapshot) {
        final ideabooks = snapshot.docs.map(_convertToIdeabook).toList();
        Logger.debug('Received ${ideabooks.length} ideabooks from Firestore');
        return ideabooks;
      });
    } catch (e) {
      Logger.error('Error listening to ideabooks', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToIdeabooks() instead to minimize Firestore reads
  /// Get all ideabooks for the current user
  @Deprecated('Use listenToIdeabooks() instead to minimize Firestore reads')
  Future<List<Ideabook>> getAllIdeabooks() async {
    Logger.debug('DEPRECATED: Getting all ideabooks from Firestore using get() - use listenToIdeabooks() instead');

    try {
      // Create a stream and get the first value
      final ideabooks = await listenToIdeabooks().first;
      Logger.debug('Retrieved ${ideabooks.length} ideabooks from Firestore using stream');
      return ideabooks;
    } catch (e) {
      Logger.error('Error getting ideabooks from Firestore', e);
      rethrow;
    }
  }

  /// Listen to a specific ideabook by ID
  /// Returns a stream of the ideabook
  Stream<Ideabook?> listenToIdeabookById(String id) {
    Logger.debug('Starting to listen to ideabook $id from Firestore');

    try {
      return _ideabooksCollection.doc(id).snapshots().map((doc) {
        if (!doc.exists) {
          Logger.debug('Ideabook $id not found in Firestore');
          return null;
        }

        final ideabook = _convertToIdeabook(doc);
        Logger.debug('Received ideabook ${ideabook.name} from Firestore');
        return ideabook;
      });
    } catch (e) {
      Logger.error('Error listening to ideabook $id', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToIdeabookById().first instead to minimize Firestore reads
  /// Get an ideabook by ID
  @Deprecated('Use listenToIdeabookById().first instead to minimize Firestore reads')
  Future<Ideabook?> getIdeabookById(String id) async {
    Logger.debug('DEPRECATED: Getting ideabook $id from Firestore using get() - use listenToIdeabookById().first instead');

    try {
      // Create a stream and get the first value
      final ideabook = await listenToIdeabookById(id).first;
      if (ideabook != null) {
        Logger.debug('Retrieved ideabook ${ideabook.name} from Firestore using stream');
      } else {
        Logger.debug('Ideabook $id not found in Firestore using stream');
      }
      return ideabook;
    } catch (e) {
      Logger.error('Error getting ideabook $id from Firestore', e);
      rethrow;
    }
  }

  /// Create a new ideabook with retry logic
  Future<Ideabook> createIdeabook(Ideabook ideabook) async {
    Logger.debug('Creating ideabook ${ideabook.name} in Firestore');

    try {
      // Generate a timestamp-based ID
      final docId = IdUtils.generateId();

      // Create the document with the generated ID using retry logic
      await RetryUtil.retry<void>(
        operation: () => _ideabooksCollection.doc(docId).set(_convertFromIdeabook(ideabook)),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'createIdeabook',
      );

      Logger.debug('Created ideabook with ID $docId in Firestore');

      // Update the ideabook with the new ID
      final newIdeabook = ideabook.copyWith(id: docId);

      return newIdeabook;
    } catch (e) {
      Logger.error('Error creating ideabook in Firestore after retries', e);
      rethrow;
    }
  }

  /// Compare two ideabooks to see if they have the same content
  /// Returns true if the content is the same, false otherwise
  bool _areIdeabooksContentEqual(Ideabook original, Ideabook updated) {
    // Compare only the content fields that would be updated in Firestore (n, c, l)
    final nameEqual = original.name == updated.name;
    final colorEqual = original.color == updated.color;
    final lockedEqual = original.isLocked == updated.isLocked;

    Logger.debug('Comparing ideabooks: name equal: $nameEqual, color equal: $colorEqual (${original.color.name}=${original.color.index} vs ${updated.color.name}=${updated.color.index}), locked equal: $lockedEqual');

    return nameEqual && colorEqual && lockedEqual;
  }

  /// Update an existing ideabook
  Future<bool> updateIdeabook(Ideabook ideabook) async {
    Logger.debug('Updating ideabook ${ideabook.id} in Firestore');
    Logger.debug('Ideabook to update: name=${ideabook.name}, color=${ideabook.color.name} (index=${ideabook.color.index})');

    try {
      // First, get the current ideabook from Firestore using the listener
      final currentIdeabook = await listenToIdeabookById(ideabook.id).first;
      if (currentIdeabook == null) {
        Logger.error('Cannot update ideabook ${ideabook.id}: Document does not exist');
        return false;
      }

      Logger.debug('Current ideabook from Firestore: name=${currentIdeabook.name}, color=${currentIdeabook.color.name} (index=${currentIdeabook.color.index})');

      // Compare the content to see if it has changed
      final contentEqual = _areIdeabooksContentEqual(currentIdeabook, ideabook);
      Logger.debug('Content equal check: $contentEqual (name: ${currentIdeabook.name == ideabook.name}, color: ${currentIdeabook.color == ideabook.color}, isLocked: ${currentIdeabook.isLocked == ideabook.isLocked})');

      if (contentEqual) {
        Logger.debug('Skipping Firestore update for ideabook ${ideabook.id} - content has not changed');
        return true; // Return success without updating Firestore
      }

      // Content has changed, proceed with the update
      final data = _convertFromIdeabook(ideabook);
      Logger.debug('Updating Firestore document with data: $data');

      // Log the document reference for debugging
      final docRef = _ideabooksCollection.doc(ideabook.id);
      Logger.debug('Document reference: ${docRef.path}');

      // Update with retry logic
      await RetryUtil.retry<void>(
        operation: () => docRef.update(data),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'updateIdeabook',
      );
      Logger.debug('Updated ideabook ${ideabook.id} in Firestore');

      // No need to verify the update - the listener will automatically update the UI
      // when the document changes in Firestore, saving an unnecessary read operation

      return true;
    } catch (e) {
      Logger.error('Error updating ideabook ${ideabook.id} in Firestore', e);
      return false;
    }
  }

  /// Delete an ideabook and all associated data
  Future<bool> deleteIdeabook(String id) async {
    Logger.debug('Deleting ideabook $id from Firestore');

    // Log authentication state
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      Logger.error('Cannot delete ideabook: User is not authenticated');
      return false;
    }

    Logger.debug('Current authenticated user: ${currentUser.uid}, email: ${currentUser.email}');
    Logger.debug('Ideabooks collection path: users/${currentUser.uid}/ideabooks');

    try {
      // Verify the ideabook exists and belongs to the current user
      final ideabook = await listenToIdeabookById(id).first;
      if (ideabook == null) {
        Logger.error('Cannot delete ideabook $id: Document does not exist');
        return false;
      }

      Logger.debug('Ideabook $id exists and belongs to current user');

      // Start a batch to delete the ideabook and all associated data
      final batch = _firestore.batch();

      // Delete associated ideas
      try {
        // Use the listener to get ideas
        final ideas = await listenToIdeas(id).first;
        for (final idea in ideas) {
          batch.delete(_ideasCollection(id).doc(idea.id));
        }
        Logger.debug('Added ${ideas.length} ideas to deletion batch');
      } catch (e) {
        Logger.error('Error getting ideas for ideabook $id', e);
        // Continue with other deletions
      }

      // Delete associated notes
      try {
        // Use the listener to get notes
        final notes = await listenToNotes(id).first;
        for (final note in notes) {
          batch.delete(_notesCollection(id).doc(note.id));
        }
        Logger.debug('Added ${notes.length} notes to deletion batch');
      } catch (e) {
        Logger.error('Error getting notes for ideabook $id', e);
        // Continue with other deletions
      }

      // Delete associated chats
      try {
        // Use a listener for chats collection
        final chatsSnapshot = await _chatsCollection(id).snapshots().first;
        for (final doc in chatsSnapshot.docs) {
          batch.delete(doc.reference);
        }
        Logger.debug('Added ${chatsSnapshot.docs.length} chats to deletion batch');
      } catch (e) {
        Logger.error('Error getting chats for ideabook $id', e);
        // Continue with other deletions
      }

      // Delete the ideabook itself
      batch.delete(_ideabooksCollection.doc(id));

      // Commit the batch with retry logic
      Logger.debug('Committing batch delete operation for ideabook $id');
      await RetryUtil.retry<void>(
        operation: () => batch.commit(),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'deleteIdeabook_batchCommit',
      );
      Logger.debug('Batch commit successful for ideabook $id');
      return true;
    } catch (e) {
      Logger.error('Error deleting ideabook $id from Firestore', e);
      return false;
    }
  }

  /// Start listening to ideas collection changes for a specific ideabook
  /// Returns a stream of ideas
  Stream<List<Idea>> listenToIdeas(String ideabookId) {
    Logger.debug('Starting to listen to ideas collection for ideabook $ideabookId');

    try {
      return _ideasCollection(ideabookId).snapshots().map((snapshot) {
        final ideas = snapshot.docs.map(_convertToIdea).toList();

        // Log the raw ideas from Firestore before sorting
        Logger.debug('===== RAW IDEAS FROM FIRESTORE (BEFORE SORTING) =====');
        Logger.debug('Received ${ideas.length} ideas from Firestore for ideabook $ideabookId');
        if (ideas.isNotEmpty) {
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            final content = idea.content.length > 20
                ? '${idea.content.substring(0, 20)}...'
                : idea.content;
            Logger.debug('Raw[$i]: effective=${idea.getEffectiveSortValue()} | t=${idea.createdAt.millisecondsSinceEpoch} | s=${idea.sortOrder ?? "n/a"} | "$content"');
          }
        }

        // Sort ideas by effective sort value in descending order
        // If sortOrder is null, use createdAt timestamp as the fallback
        ideas.sort((a, b) {
          final aValue = a.getEffectiveSortValue();
          final bValue = b.getEffectiveSortValue();

          // Log comparison details for debugging
          final aContent = a.content.length > 15 ? '${a.content.substring(0, 15)}...' : a.content;
          final bContent = b.content.length > 15 ? '${b.content.substring(0, 15)}...' : b.content;
          final comparison = bValue.compareTo(aValue);
          Logger.debug('Comparing: effective_a=${a.getEffectiveSortValue()} | t_a=${a.createdAt.millisecondsSinceEpoch} | s_a=${a.sortOrder ?? "n/a"} | "$aContent" vs effective_b=${b.getEffectiveSortValue()} | t_b=${b.createdAt.millisecondsSinceEpoch} | s_b=${b.sortOrder ?? "n/a"} | "$bContent" = $comparison');

          return comparison; // Descending order
        });

        Logger.debug('===== SORTED IDEAS =====');
        Logger.debug('Sorted ${ideas.length} ideas for ideabook $ideabookId');

        // Log all ideas after sorting for debugging
        if (ideas.isNotEmpty) {
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            final content = idea.content.length > 20
                ? '${idea.content.substring(0, 20)}...'
                : idea.content;
            Logger.debug('Sorted[$i]: effective=${idea.getEffectiveSortValue()} | t=${idea.createdAt.millisecondsSinceEpoch} | s=${idea.sortOrder ?? "n/a"} | "$content"');
          }
        }

        return ideas;
      });
    } catch (e) {
      Logger.error('Error listening to ideas for ideabook $ideabookId', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToIdeas() instead to minimize Firestore reads
  /// Get all ideas for a specific ideabook
  @Deprecated('Use listenToIdeas() instead to minimize Firestore reads')
  Future<List<Idea>> getIdeasByIdeabookId(String ideabookId) async {
    Logger.debug('DEPRECATED: Getting all ideas for ideabook $ideabookId from Firestore using get() - use listenToIdeas() instead');

    try {
      // Create a stream and get the first value
      final ideas = await listenToIdeas(ideabookId).first;

      // Log detailed information about the ideas
      Logger.debug('Retrieved ${ideas.length} ideas from Firestore for ideabook $ideabookId using stream');
      if (ideas.isEmpty) {
        Logger.debug('WARNING: No ideas found in Firestore for ideabook $ideabookId');
      } else {
        // Log the first idea's content with safe substring handling
        final firstIdea = ideas.first;
        final contentPreview = firstIdea.content.length > 30
            ? '${firstIdea.content.substring(0, 30)}...'
            : firstIdea.content;
        Logger.debug('First idea content: "$contentPreview"');
      }

      return ideas;
    } catch (e) {
      Logger.error('Error getting ideas for ideabook $ideabookId from Firestore', e);
      rethrow;
    }
  }

  /// Listen to a specific idea by ID
  /// Returns a stream of the idea
  Stream<Idea?> listenToIdeaById(String ideabookId, String ideaId) {
    Logger.debug('Starting to listen to idea $ideaId from Firestore');

    try {
      return _ideasCollection(ideabookId).doc(ideaId).snapshots().map((doc) {
        if (!doc.exists) {
          Logger.debug('Idea $ideaId not found in Firestore');
          return null;
        }

        final idea = _convertToIdea(doc);
        Logger.debug('Received idea $ideaId from Firestore');
        return idea;
      });
    } catch (e) {
      Logger.error('Error listening to idea $ideaId', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToIdeaById().first instead to minimize Firestore reads
  /// Get an idea by ID
  @Deprecated('Use listenToIdeaById().first instead to minimize Firestore reads')
  Future<Idea?> getIdeaById(String ideabookId, String ideaId) async {
    Logger.debug('DEPRECATED: Getting idea $ideaId from Firestore using get() - use listenToIdeaById().first instead');

    try {
      // Create a stream and get the first value
      final idea = await listenToIdeaById(ideabookId, ideaId).first;
      if (idea != null) {
        Logger.debug('Retrieved idea $ideaId from Firestore using stream');
      } else {
        Logger.debug('Idea $ideaId not found in Firestore using stream');
      }
      return idea;
    } catch (e) {
      Logger.error('Error getting idea $ideaId from Firestore', e);
      rethrow;
    }
  }

  /// Create a new idea with retry logic
  Future<Idea> createIdea(String ideabookId, Idea idea) async {
    Logger.debug('Creating idea for ideabook $ideabookId in Firestore');

    try {
      // Generate a timestamp-based ID
      final docId = IdUtils.generateId();

      // Create the document with the generated ID using retry logic
      await RetryUtil.retry<void>(
        operation: () => _ideasCollection(ideabookId).doc(docId).set(_convertFromIdea(idea, isNewIdea: true)),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'createIdea',
      );

      Logger.debug('Created idea with ID $docId in Firestore');

      // Update the idea with the new ID
      final newIdea = idea.copyWith(id: docId);

      return newIdea;
    } catch (e) {
      Logger.error('Error creating idea in Firestore after retries', e);
      rethrow;
    }
  }

  /// Compare two ideas to see if they have the same content
  /// Returns true if the content is the same, false otherwise
  bool _areIdeasContentEqual(Idea original, Idea updated) {
    // Compare only the content field that would be updated in Firestore
    return original.content == updated.content;
  }

  /// Update an existing idea
  Future<bool> updateIdea(String ideabookId, Idea idea) async {
    Logger.debug('Updating idea ${idea.id} in Firestore');
    Logger.debug('Idea content: "${idea.content.substring(0, idea.content.length.clamp(0, 20))}${idea.content.length > 20 ? "..." : ""}"');

    try {
      // First, get the current idea from Firestore using the listener
      final currentIdea = await listenToIdeaById(ideabookId, idea.id).first;
      if (currentIdea == null) {
        Logger.error('Cannot update idea ${idea.id}: Document does not exist');
        return false;
      }

      // Compare the content to see if it has changed
      if (_areIdeasContentEqual(currentIdea, idea) && currentIdea.sortOrder == idea.sortOrder) {
        Logger.debug('Skipping Firestore update for idea ${idea.id} - content and sort order have not changed');
        return true; // Return success without updating Firestore
      }

      // Content has changed, proceed with the update
      final data = _convertFromIdea(idea);
      Logger.debug('Converted data: $data');

      // Update with retry logic
      await RetryUtil.retry<void>(
        operation: () => _ideasCollection(ideabookId).doc(idea.id).update(data),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'updateIdea',
      );
      Logger.debug('Updated idea ${idea.id} in Firestore successfully');

      // No need to verify the update - the listener will automatically update the UI
      // when the document changes in Firestore, saving an unnecessary read operation

      return true;
    } catch (e) {
      Logger.error('Error updating idea ${idea.id} in Firestore', e);
      return false;
    }
  }

  /// Update the sort order of an idea
  /// This is used when reordering ideas via drag-and-drop
  Future<bool> updateIdeaSortOrder(String ideabookId, String ideaId, double newSortOrder) async {
    Logger.debug('Updating sort order for idea $ideaId to $newSortOrder');

    try {
      // Get the current idea from Firestore
      final currentIdea = await listenToIdeaById(ideabookId, ideaId).first;
      if (currentIdea == null) {
        Logger.error('Cannot update sort order for idea $ideaId: Document does not exist');
        return false;
      }

      // Log detailed information about the idea being updated
      final content = currentIdea.content.length > 30
          ? '${currentIdea.content.substring(0, 30)}...'
          : currentIdea.content;
      Logger.debug('===== UPDATING IDEA SORT ORDER =====');
      Logger.debug('Idea: "$content"');
      Logger.debug('Current values:');
      Logger.debug('- ID: ${currentIdea.id}');
      Logger.debug('- effective: ${currentIdea.getEffectiveSortValue()}');
      Logger.debug('- t: ${currentIdea.createdAt.millisecondsSinceEpoch}');
      Logger.debug('- s: ${currentIdea.sortOrder ?? "n/a"}');
      Logger.debug('- New sort order to set: $newSortOrder');
      Logger.debug('=====================================');

      // Create an updated idea with the new sort order
      final updatedIdea = currentIdea.copyWith(
        sortOrder: newSortOrder,
        updatedAt: DateTime.now(),
      );

      // Update the idea in Firestore
      final result = await updateIdea(ideabookId, updatedIdea);

      if (result) {
        Logger.debug('Successfully updated sort order for idea $ideaId to $newSortOrder');
        Logger.debug('New effective sort value: ${updatedIdea.getEffectiveSortValue()}');
      } else {
        Logger.error('Failed to update sort order for idea $ideaId');
      }

      return result;
    } catch (e) {
      Logger.error('Error updating sort order for idea $ideaId', e);
      return false;
    }
  }

  /// Delete an idea with retry logic
  Future<bool> deleteIdea(String ideabookId, String ideaId) async {
    Logger.debug('Deleting idea $ideaId from Firestore');

    try {
      // Delete with retry logic
      await RetryUtil.retry<void>(
        operation: () => _ideasCollection(ideabookId).doc(ideaId).delete(),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'deleteIdea',
      );
      Logger.debug('Deleted idea $ideaId from Firestore');
      return true;
    } catch (e) {
      Logger.error('Error deleting idea $ideaId from Firestore after retries', e);
      return false;
    }
  }

  /// Start listening to notes collection changes for a specific ideabook
  /// Returns a stream of notes
  Stream<List<Note>> listenToNotes(String ideabookId) {
    Logger.debug('Starting to listen to notes collection for ideabook $ideabookId');

    try {
      return _notesCollection(ideabookId).snapshots().map((snapshot) {
        final notes = snapshot.docs.map(_convertToNote).toList();

        // Log the raw notes from Firestore before sorting
        Logger.debug('===== RAW NOTES FROM FIRESTORE (BEFORE SORTING) =====');
        Logger.debug('Received ${notes.length} notes from Firestore for ideabook $ideabookId');
        if (notes.isNotEmpty) {
          for (int i = 0; i < notes.length; i++) {
            final note = notes[i];
            final title = note.title.length > 20
                ? '${note.title.substring(0, 20)}...'
                : note.title;
            Logger.debug('Raw[$i]: effective=${note.getEffectiveSortValue()} | t=${note.createdAt.millisecondsSinceEpoch} | s=${note.sortOrder ?? "n/a"} | "$title"');
          }
        }

        // Sort notes by effective sort value in descending order
        // If sortOrder is null, use createdAt timestamp as the fallback
        notes.sort((a, b) {
          final aValue = a.getEffectiveSortValue();
          final bValue = b.getEffectiveSortValue();

          // Log comparison details for debugging
          final aTitle = a.title.length > 15 ? '${a.title.substring(0, 15)}...' : a.title;
          final bTitle = b.title.length > 15 ? '${b.title.substring(0, 15)}...' : b.title;
          final comparison = bValue.compareTo(aValue);
          Logger.debug('Comparing: effective_a=${a.getEffectiveSortValue()} | t_a=${a.createdAt.millisecondsSinceEpoch} | s_a=${a.sortOrder ?? "n/a"} | "$aTitle" vs effective_b=${b.getEffectiveSortValue()} | t_b=${b.createdAt.millisecondsSinceEpoch} | s_b=${b.sortOrder ?? "n/a"} | "$bTitle" = $comparison');

          return comparison; // Descending order
        });

        Logger.debug('===== SORTED NOTES =====');
        Logger.debug('Sorted ${notes.length} notes for ideabook $ideabookId');

        // Log all notes after sorting for debugging
        if (notes.isNotEmpty) {
          for (int i = 0; i < notes.length; i++) {
            final note = notes[i];
            final title = note.title.length > 20
                ? '${note.title.substring(0, 20)}...'
                : note.title;
            Logger.debug('Sorted[$i]: effective=${note.getEffectiveSortValue()} | t=${note.createdAt.millisecondsSinceEpoch} | s=${note.sortOrder ?? "n/a"} | "$title"');
          }
        }

        return notes;
      });
    } catch (e) {
      Logger.error('Error listening to notes for ideabook $ideabookId', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToNotes() instead to minimize Firestore reads
  /// Get all notes for a specific ideabook
  @Deprecated('Use listenToNotes() instead to minimize Firestore reads')
  Future<List<Note>> getNotesByIdeabookId(String ideabookId) async {
    Logger.debug('DEPRECATED: Getting all notes for ideabook $ideabookId from Firestore using get() - use listenToNotes() instead');

    try {
      // Create a stream and get the first value
      final notes = await listenToNotes(ideabookId).first;
      Logger.debug('Retrieved ${notes.length} notes from Firestore for ideabook $ideabookId using stream');
      return notes;
    } catch (e) {
      Logger.error('Error getting notes for ideabook $ideabookId from Firestore', e);
      rethrow;
    }
  }

  /// Listen to a specific note by ID
  /// Returns a stream of the note
  Stream<Note?> listenToNoteById(String ideabookId, String noteId) {
    Logger.debug('Starting to listen to note $noteId from Firestore');

    try {
      return _notesCollection(ideabookId).doc(noteId).snapshots().map((doc) {
        if (!doc.exists) {
          Logger.debug('Note $noteId not found in Firestore');
          return null;
        }

        final note = _convertToNote(doc);
        Logger.debug('Received note $noteId from Firestore');
        return note;
      });
    } catch (e) {
      Logger.error('Error listening to note $noteId', e);
      rethrow;
    }
  }

  /// DEPRECATED: Use listenToNoteById().first instead to minimize Firestore reads
  /// Get a note by ID
  @Deprecated('Use listenToNoteById().first instead to minimize Firestore reads')
  Future<Note?> getNoteById(String ideabookId, String noteId) async {
    Logger.debug('DEPRECATED: Getting note $noteId from Firestore using get() - use listenToNoteById().first instead');

    try {
      // Create a stream and get the first value
      final note = await listenToNoteById(ideabookId, noteId).first;
      if (note != null) {
        Logger.debug('Retrieved note $noteId from Firestore using stream');
      } else {
        Logger.debug('Note $noteId not found in Firestore using stream');
      }
      return note;
    } catch (e) {
      Logger.error('Error getting note $noteId from Firestore', e);
      rethrow;
    }
  }

  /// Create a new note with retry logic
  Future<Note> createNote(String ideabookId, Note note) async {
    Logger.debug('Creating note for ideabook $ideabookId in Firestore');

    try {
      // Generate a timestamp-based ID
      final docId = IdUtils.generateId();

      // Create the document with the generated ID using retry logic
      await RetryUtil.retry<void>(
        operation: () => _notesCollection(ideabookId).doc(docId).set(_convertFromNote(note, isNewNote: true)),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'createNote',
      );

      Logger.debug('Created note with ID $docId in Firestore');

      // Update the note with the new ID
      final newNote = note.copyWith(id: docId);

      return newNote;
    } catch (e) {
      Logger.error('Error creating note in Firestore after retries', e);
      rethrow;
    }
  }

  /// Compare two notes to see if they have the same content
  /// Returns true if the content is the same, false otherwise
  bool _areNotesContentEqual(Note original, Note updated) {
    // Compare only the content fields that would be updated in Firestore
    return original.title == updated.title &&
           original.content == updated.content &&
           original.sortOrder == updated.sortOrder;
  }

  /// Update an existing note
  Future<bool> updateNote(String ideabookId, Note note) async {
    Logger.debug('Updating note ${note.id} in Firestore');

    try {
      // First, get the current note from Firestore using the listener
      final currentNote = await listenToNoteById(ideabookId, note.id).first;
      if (currentNote == null) {
        Logger.error('Cannot update note ${note.id}: Document does not exist');
        return false;
      }

      // Compare the content to see if it has changed
      if (_areNotesContentEqual(currentNote, note)) {
        Logger.debug('Skipping Firestore update for note ${note.id} - content has not changed');
        return true; // Return success without updating Firestore
      }

      // Content has changed, proceed with the update
      // Use isNewNote: false to ensure we don't overwrite the 'r' field
      final data = _convertFromNote(note, isNewNote: false);
      Logger.debug('Converted data: $data');

      // Update with retry logic
      await RetryUtil.retry<void>(
        operation: () => _notesCollection(ideabookId).doc(note.id).update(data),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'updateNote',
      );
      Logger.debug('Updated note ${note.id} in Firestore successfully');

      return true;
    } catch (e) {
      Logger.error('Error updating note ${note.id} in Firestore', e);
      return false;
    }
  }

  /// Update the sort order of a note
  /// This is used when reordering notes via drag-and-drop
  Future<bool> updateNoteSortOrder(String ideabookId, String noteId, double newSortOrder) async {
    Logger.debug('Updating sort order for note $noteId to $newSortOrder');

    try {
      // Get the current note from Firestore
      final currentNote = await listenToNoteById(ideabookId, noteId).first;
      if (currentNote == null) {
        Logger.error('Cannot update sort order for note $noteId: Document does not exist');
        return false;
      }

      // Log detailed information about the note being updated
      final title = currentNote.title.length > 30
          ? '${currentNote.title.substring(0, 30)}...'
          : currentNote.title;
      Logger.debug('===== UPDATING NOTE SORT ORDER =====');
      Logger.debug('Note: "$title"');
      Logger.debug('Current values:');
      Logger.debug('- ID: ${currentNote.id}');
      Logger.debug('- effective: ${currentNote.getEffectiveSortValue()}');
      Logger.debug('- t: ${currentNote.createdAt.millisecondsSinceEpoch}');
      Logger.debug('- s: ${currentNote.sortOrder ?? "n/a"}');
      Logger.debug('- New sort order to set: $newSortOrder');
      Logger.debug('=====================================');

      // Create an updated note with the new sort order
      final updatedNote = currentNote.copyWith(
        sortOrder: newSortOrder,
        updatedAt: DateTime.now(),
      );

      // Update the note in Firestore
      final result = await updateNote(ideabookId, updatedNote);

      if (result) {
        Logger.debug('Successfully updated sort order for note $noteId to $newSortOrder');
        Logger.debug('New effective sort value: ${updatedNote.getEffectiveSortValue()}');
      } else {
        Logger.error('Failed to update sort order for note $noteId');
      }

      return result;
    } catch (e) {
      Logger.error('Error updating sort order for note $noteId', e);
      return false;
    }
  }

  /// Delete a note with retry logic
  Future<bool> deleteNote(String ideabookId, String noteId) async {
    Logger.debug('Deleting note $noteId from Firestore');

    try {
      // Delete with retry logic
      await RetryUtil.retry<void>(
        operation: () => _notesCollection(ideabookId).doc(noteId).delete(),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'deleteNote',
      );
      Logger.debug('Deleted note $noteId from Firestore');
      return true;
    } catch (e) {
      Logger.error('Error deleting note $noteId from Firestore after retries', e);
      return false;
    }
  }

  /// Get the user document reference
  DocumentReference<Map<String, dynamic>> get _userDocument {
    if (_userId == null) {
      throw Exception('User is not signed in');
    }
    return _firestore.collection('users').doc(_userId);
  }

  /// Save the passcode hash to the user document with retry logic
  Future<bool> savePasscodeHash(String passcodeHash) async {
    Logger.debug('Saving passcode hash to user document');

    try {
      // Save with retry logic
      await RetryUtil.retry<void>(
        operation: () => _userDocument.set({
          'p': passcodeHash, // Use short field name 'p' for passcode
        }, SetOptions(merge: true)),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'savePasscodeHash',
      );
      Logger.debug('Passcode hash saved successfully');
      return true;
    } catch (e) {
      Logger.error('Error saving passcode hash after retries', e);
      return false;
    }
  }

  /// Listen to the passcode hash in the user document
  /// Returns a stream of the passcode hash
  Stream<String?> listenToPasscodeHash() {
    Logger.debug('Starting to listen to passcode hash from user document');

    try {
      // Log the current user ID for debugging
      Logger.debug('Current user ID: ${_userId ?? "null"}');

      // Check if user is authenticated
      if (_userId == null) {
        Logger.error('Cannot listen to passcode hash: User is not authenticated');
        return Stream.value(null);
      }

      // Log the user document path
      Logger.debug('User document path: users/$_userId');

      return _userDocument.snapshots().map((doc) {
        if (!doc.exists) {
          Logger.debug('User document does not exist');
          return null;
        }

        final data = doc.data();
        if (data == null) {
          Logger.debug('User document data is null');
          return null;
        }

        // Log all fields in the user document for debugging
        Logger.debug('User document fields: ${data.keys.join(', ')}');

        // Check for both old 'passcode' field and new 'p' field for backward compatibility
        if (!data.containsKey('p') && !data.containsKey('passcode')) {
          Logger.debug('No passcode hash found in user document');
          return null;
        }

        // Prefer the new 'p' field, but fall back to the old 'passcode' field
        final passcodeHash = data.containsKey('p')
            ? data['p'] as String
            : data['passcode'] as String;
        Logger.debug('Passcode hash received from Firestore: ${passcodeHash.substring(0, 10)}...');
        return passcodeHash;
      });
    } catch (e) {
      Logger.error('Error listening to passcode hash', e);
      return Stream.error(e);
    }
  }

  /// DEPRECATED: Use listenToPasscodeHash().first instead to minimize Firestore reads
  /// Get the passcode hash from the user document
  @Deprecated('Use listenToPasscodeHash().first instead to minimize Firestore reads')
  Future<String?> getPasscodeHash() async {
    Logger.debug('DEPRECATED: Getting passcode hash from Firestore using get() - use listenToPasscodeHash().first instead');

    try {
      // Check if user is authenticated
      if (_userId == null) {
        Logger.error('Cannot get passcode hash: User is not authenticated');
        return null;
      }

      // Create a stream and get the first value
      final passcodeHash = await listenToPasscodeHash().first;
      if (passcodeHash != null) {
        Logger.debug('Retrieved passcode hash from Firestore using stream');
      } else {
        Logger.debug('No passcode hash found in Firestore using stream');
      }
      return passcodeHash;
    } catch (e) {
      Logger.error('Error getting passcode hash from Firestore', e);
      return null;
    }
  }

  /// Check if a passcode has been set
  Future<bool> isPasscodeSet() async {
    Logger.debug('Checking if passcode is set');

    try {
      // Check if user is authenticated
      if (_userId == null) {
        Logger.error('Cannot check if passcode is set: User is not authenticated');
        return false;
      }

      final passcodeHash = await listenToPasscodeHash().first;
      final isSet = passcodeHash != null && passcodeHash.isNotEmpty;
      Logger.debug('Passcode is ${isSet ? 'set' : 'not set'}');
      return isSet;
    } catch (e) {
      Logger.error('Error checking if passcode is set', e);
      return false;
    }
  }

  /// Validate the given passcode against the stored hash
  Future<bool> validatePasscode(String passcode) async {
    Logger.debug('Validating passcode');

    try {
      final storedHash = await listenToPasscodeHash().first;

      if (storedHash == null || storedHash.isEmpty) {
        Logger.debug('No passcode hash found to validate against');
        return false;
      }

      final isValid = CryptoUtils.validatePasscode(passcode, storedHash);
      Logger.debug('Passcode validation result: $isValid');

      return isValid;
    } catch (e) {
      Logger.error('Error validating passcode', e);
      return false;
    }
  }
}
