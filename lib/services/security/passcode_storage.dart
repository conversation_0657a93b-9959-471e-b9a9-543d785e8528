import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/services/firebase/firestore_service.dart';
import 'package:voji/ui/providers/passcode_provider.dart';
import 'package:voji/utils/crypto_utils.dart';
import 'package:voji/utils/logger.dart';

/// Service for storing and retrieving the passcode
class PasscodeStorage {
  /// Firestore service instance
  static final FirestoreService _firestoreService = FirestoreService(
    auth: FirebaseAuth.instance,
  );

  /// Firestore listener pool instance
  static final FirestoreListenerPool _listenerPool = FirestoreListenerPool(_firestoreService);

  /// Reference to the ProviderContainer for updating the cached state
  static ProviderContainer? _container;

  /// Initialize the container reference
  static void initializeContainer(ProviderContainer container) {
    _container = container;
    Logger.debug('PasscodeStorage: Container initialized');
  }

  /// Ensure the passcode hash listener is set up
  /// This helps prevent race conditions when checking the passcode on app startup
  static Future<void> _ensurePasscodeHashListener() async {
    Logger.debug('Ensuring passcode hash listener is set up');

    try {
      // Get the stream from the listener pool, which will create a listener if one doesn't exist
      _listenerPool.getPasscodeHashStream();

      // Wait a short time to allow the listener to initialize
      await Future.delayed(const Duration(milliseconds: 300));

      Logger.debug('Passcode hash listener is now set up');
    } catch (e) {
      Logger.error('Error ensuring passcode hash listener', e);
    }
  }

  /// Update the cached passcode state
  static void _updateCachedState(bool isSet) {
    if (_container != null) {
      try {
        // Update the cached state
        _container!.read(passcodeSetCachedProvider.notifier).updateState(isSet);
        Logger.debug('PasscodeStorage: Updated cached state to isSet=$isSet');
      } catch (e) {
        Logger.error('PasscodeStorage: Error updating cached state', e);
      }
    }
  }

  /// Save the passcode to Firestore
  /// The passcode is hashed before saving
  static Future<bool> savePasscode(String passcode) async {
    try {
      Logger.debug('Hashing and saving passcode to Firestore');

      // Hash the passcode before saving
      final hashedPasscode = CryptoUtils.hashPasscode(passcode);

      // Save the hashed passcode to Firestore
      final result = await _firestoreService.savePasscodeHash(hashedPasscode);

      if (result) {
        Logger.debug('Passcode hash saved successfully to Firestore');
        // Update the cached state to indicate a passcode is set
        _updateCachedState(true);
      } else {
        Logger.error('Failed to save passcode hash to Firestore');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving passcode', e);
      return false;
    }
  }

  /// Check if a passcode has been set in Firestore
  static Future<bool> isPasscodeSet() async {
    try {
      // Ensure the passcode hash listener is set up first
      await _ensurePasscodeHashListener();

      // Now check if the passcode is set
      final result = await _firestoreService.isPasscodeSet();
      Logger.debug('isPasscodeSet check result: $result');

      // Update the cached state
      _updateCachedState(result);

      return result;
    } catch (e) {
      Logger.error('Error checking if passcode is set', e);
      return false;
    }
  }

  /// Clear the saved passcode from Firestore
  static Future<bool> clearPasscode() async {
    try {
      // Save an empty string to effectively clear the passcode
      final result = await _firestoreService.savePasscodeHash('');

      Logger.debug('Passcode cleared from Firestore: $result');

      if (result) {
        // Update the cached state to indicate no passcode is set
        _updateCachedState(false);
      }

      return result;
    } catch (e) {
      Logger.error('Error clearing passcode', e);
      return false;
    }
  }

  /// Validate the given passcode against the stored hash in Firestore
  static Future<bool> validatePasscode(String passcode) async {
    try {
      // Ensure the passcode hash listener is set up first
      await _ensurePasscodeHashListener();

      // Now validate the passcode
      final result = await _firestoreService.validatePasscode(passcode);
      Logger.debug('validatePasscode result: $result');
      return result;
    } catch (e) {
      Logger.error('Error validating passcode', e);
      return false;
    }
  }
}
