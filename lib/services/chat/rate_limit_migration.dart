import 'package:shared_preferences/shared_preferences.dart';
import 'package:voji/services/chat/chat_rate_limit_cache.dart';
import 'package:voji/services/preferences/chat_rate_limit_storage.dart';
import 'package:voji/utils/logger.dart';

/// Utility class for migrating from the old rate limit system to the new one
class RateLimitMigration {
  /// Key for tracking if migration has already been performed
  static const String _migrationCompletedKey = 'rate_limit_migration_completed';

  /// Migrate from the old rate limit system to the new one
  /// This should be called once at app startup
  static Future<void> migrateToNewSystem() async {
    Logger.debug('===== CHECKING RATE LIMIT MIGRATION STATUS =====');

    try {
      // Check if migration has already been performed
      final prefs = await SharedPreferences.getInstance();
      final migrationCompleted = prefs.getBool(_migrationCompletedKey) ?? false;

      if (migrationCompleted) {
        Logger.debug('Rate limit migration already completed, skipping');
        return;
      }

      Logger.debug('===== STARTING RATE LIMIT MIGRATION =====');

      // Load the existing message log from storage
      final log = await ChatRateLimitStorage.loadMessageLog();
      Logger.debug('Loaded ${log.messageTimes.length} messages from storage');

      if (log.messageTimes.isEmpty) {
        Logger.debug('No messages to migrate, marking migration as complete');
        await prefs.setBool(_migrationCompletedKey, true);
        return;
      }

      // Create a new cache
      final cache = ChatRateLimitCache();

      // Wait for the cache to initialize
      await cache.initialized;

      // Instead of adding messages one by one (which would cause duplication),
      // directly set the cache's message times and save once
      await cache.setMessageTimes(log.messageTimes);

      // Mark migration as completed
      await prefs.setBool(_migrationCompletedKey, true);

      Logger.debug('Migrated ${log.messageTimes.length} messages to new cache');
      Logger.debug('===== RATE LIMIT MIGRATION COMPLETE =====');
    } catch (e) {
      Logger.error('Error migrating rate limit system', e);
    }
  }
}
