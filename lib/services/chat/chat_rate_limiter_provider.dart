import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/chat/chat_rate_limiter.dart';

/// Provider for the chat rate limiter
final chatRateLimiterProvider = Provider<ChatRateLimiter>((ref) {
  return ChatRateLimiter();
});

/// Provider for the current rate limit status
final chatRateLimitStatusProvider = StateProvider<RateLimitResult?>((ref) => null);

/// Provider for checking the rate limit status
final checkChatRateLimitProvider = FutureProvider<RateLimitResult>((ref) async {
  final rateLimiter = ref.watch(chatRateLimiterProvider);
  final result = await rateLimiter.checkRateLimit();
  
  // Update the status provider
  ref.read(chatRateLimitStatusProvider.notifier).state = result;
  
  return result;
});
