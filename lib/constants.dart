/// App-wide constants

/// Maximum number of ideas per ideabook
const int kMaxIdeasPerIdeabook = 100;

/// Maximum number of notes per ideabook
const int kMaxNotesPerIdeabook = 100;

/// Maximum number of ideabooks per user
const int kMaxIdeabooksPerUser = 1000;

/// Maximum length for ideabook name
const int kMaxIdeabookNameLength = 100;

/// Maximum length for idea content
const int kMaxIdeaContentLength = 1000;

/// Maximum duration for audio recording in seconds
const int kMaxAudioRecordingDurationSeconds = 60;

/// Duration in seconds for the countdown warning before recording stops
const int kRecordingCountdownWarningSeconds = 10;
